// E2E测试示例 - 用户完整流程测试
// 注意：这是一个示例文件，实际使用需要安装Playwright或Cypress

/*
// 如果使用Playwright，需要先安装：
// npm install -D @playwright/test

import { test, expect } from '@playwright/test'

test.describe('用户完整流程', () => {
  test.beforeEach(async ({ page }) => {
    // 访问首页
    await page.goto('/')
  })

  test('用户注册和登录流程', async ({ page }) => {
    // 点击注册按钮
    await page.click('text=注册')
    
    // 填写注册表单
    await page.fill('[name="email"]', '<EMAIL>')
    await page.fill('[name="username"]', 'testuser')
    await page.fill('[name="fullName"]', 'Test User')
    await page.fill('[name="password"]', 'password123')
    await page.fill('[name="confirmPassword"]', 'password123')
    await page.check('[name="agreeTerms"]')
    
    // 提交注册
    await page.click('button[type="submit"]')
    
    // 验证跳转到邮箱验证页面
    await expect(page).toHaveURL(/\/auth\/verify-email/)
    await expect(page.locator('text=验证您的邮箱')).toBeVisible()
  })

  test('应用发布流程', async ({ page }) => {
    // 模拟已登录状态
    await page.goto('/apps/create')
    
    // 填写应用信息
    await page.fill('[name="name"]', '测试应用')
    await page.fill('[name="description"]', '这是一个测试应用的描述')
    await page.selectOption('[name="category"]', 'productivity')
    await page.fill('[name="developer"]', '测试开发者')
    await page.fill('[name="website_url"]', 'https://example.com')
    
    // 提交表单
    await page.click('button[type="submit"]')
    
    // 验证应用创建成功
    await expect(page).toHaveURL(/\/apps\/[a-zA-Z0-9-]+/)
    await expect(page.locator('text=测试应用')).toBeVisible()
  })

  test('送码活动创建流程', async ({ page }) => {
    // 假设已有应用，访问活动创建页面
    await page.goto('/apps/test-app-id/activities/create')
    
    // 填写活动信息
    await page.fill('[name="title"]', '限时免费活动')
    await page.fill('[name="description"]', '限时免费获取应用邀请码')
    
    // 添加邀请码
    await page.fill('[name="invite_codes.0"]', 'TEST-CODE-001')
    await page.click('text=添加邀请码')
    await page.fill('[name="invite_codes.1"]', 'TEST-CODE-002')
    
    // 提交活动
    await page.click('button[type="submit"]')
    
    // 验证活动创建成功
    await expect(page).toHaveURL(/\/activities\/[a-zA-Z0-9-]+/)
    await expect(page.locator('text=限时免费活动')).toBeVisible()
  })

  test('邀请码领取流程', async ({ page }) => {
    // 访问活动页面
    await page.goto('/activities/test-activity-id')
    
    // 点击领取邀请码
    await page.click('text=领取邀请码')
    
    // 验证领取成功
    await expect(page.locator('text=领取成功')).toBeVisible()
    await expect(page.locator('[data-testid="invite-code"]')).toBeVisible()
  })

  test('用户个人中心', async ({ page }) => {
    // 访问个人中心
    await page.goto('/profile')
    
    // 验证页面元素
    await expect(page.locator('text=个人中心')).toBeVisible()
    await expect(page.locator('[data-testid="user-stats"]')).toBeVisible()
    await expect(page.locator('[data-testid="quick-actions"]')).toBeVisible()
  })

  test('搜索和筛选功能', async ({ page }) => {
    // 访问应用列表页面
    await page.goto('/apps')
    
    // 使用搜索功能
    await page.fill('[data-testid="search-input"]', '测试应用')
    await page.press('[data-testid="search-input"]', 'Enter')
    
    // 验证搜索结果
    await expect(page.locator('[data-testid="app-card"]')).toBeVisible()
    
    // 使用分类筛选
    await page.selectOption('[data-testid="category-filter"]', 'productivity')
    
    // 验证筛选结果
    await expect(page.locator('text=productivity')).toBeVisible()
  })

  test('响应式设计测试', async ({ page }) => {
    // 测试移动端视图
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto('/')
    
    // 验证移动端导航
    await expect(page.locator('[data-testid="mobile-menu-button"]')).toBeVisible()
    
    // 打开移动端菜单
    await page.click('[data-testid="mobile-menu-button"]')
    await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible()
    
    // 测试平板视图
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.reload()
    
    // 测试桌面视图
    await page.setViewportSize({ width: 1920, height: 1080 })
    await page.reload()
  })

  test('性能测试', async ({ page }) => {
    // 监控页面加载性能
    const startTime = Date.now()
    await page.goto('/')
    const loadTime = Date.now() - startTime
    
    // 验证页面加载时间在合理范围内
    expect(loadTime).toBeLessThan(3000) // 3秒内加载完成
    
    // 检查关键元素是否可见
    await expect(page.locator('text=送码网')).toBeVisible()
    await expect(page.locator('[data-testid="hero-section"]')).toBeVisible()
  })

  test('错误处理测试', async ({ page }) => {
    // 测试404页面
    await page.goto('/non-existent-page')
    await expect(page.locator('text=404')).toBeVisible()
    await expect(page.locator('text=页面未找到')).toBeVisible()
    
    // 测试返回首页功能
    await page.click('text=返回首页')
    await expect(page).toHaveURL('/')
  })
})

test.describe('API测试', () => {
  test('健康检查API', async ({ request }) => {
    const response = await request.get('/api/health')
    expect(response.status()).toBe(200)
    
    const data = await response.json()
    expect(data.status).toBe('healthy')
  })

  test('应用列表API', async ({ request }) => {
    const response = await request.get('/api/apps')
    expect(response.status()).toBe(200)
    
    const data = await response.json()
    expect(Array.isArray(data.apps)).toBe(true)
  })
})
*/

// 实际的测试配置文件
export const e2eConfig = {
  baseURL: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  testTimeout: 30000,
  retries: 2,
  workers: 1,
  
  // 测试用户数据
  testUsers: {
    regular: {
      email: '<EMAIL>',
      password: 'password123',
      username: 'testuser'
    },
    vip: {
      email: '<EMAIL>',
      password: 'password123',
      username: 'vipuser'
    }
  },
  
  // 测试数据
  testData: {
    app: {
      name: '测试应用',
      description: '这是一个用于E2E测试的应用',
      category: 'productivity',
      developer: '测试开发者',
      website_url: 'https://example.com'
    },
    activity: {
      title: '测试活动',
      description: '这是一个用于E2E测试的活动',
      invite_codes: ['TEST-001', 'TEST-002', 'TEST-003']
    }
  }
}

// 测试工具函数
export const testUtils = {
  // 等待元素出现
  waitForElement: (selector: string, timeout: number = 5000) => {
    // 实现等待逻辑
  },
  
  // 模拟用户登录
  loginUser: async (email: string, password: string) => {
    // 实现登录逻辑
  },
  
  // 清理测试数据
  cleanupTestData: async () => {
    // 实现数据清理逻辑
  }
}

export default e2eConfig
