# 应用推荐网站 - 基于送码活动的应用发现平台

一个创新的应用发现平台，通过送码活动的热度来推荐应用，而不是传统的下载量排名。

## 🌟 项目特色

- **送码活动驱动**: 基于送码活动数量和热度的应用排名系统
- **VIP会员功能**: VIP用户可设置高级活动条件（实名认证、应用订阅、反馈要求）
- **实名认证系统**: 支持身份验证，提高活动参与门槛
- **现代化UI**: 响应式设计，支持移动端和桌面端
- **完整的权限控制**: 细粒度的用户权限和功能访问控制

## 🚀 技术栈

- **前端**: Next.js 14, React 18, TypeScript, Tailwind CSS
- **后端**: Next.js API Routes, Supabase
- **数据库**: PostgreSQL (Supabase)
- **认证**: Supabase Auth (支持邮箱密码和OAuth)
- **部署**: Vercel (推荐)

## 📦 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd app-recommendation-site
```

### 2. 安装依赖

```bash
npm install
```

### 3. 环境配置

复制环境变量模板：

```bash
cp .env.local.example .env.local
```

编辑 `.env.local` 文件，填入您的Supabase配置：

```bash
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 4. 数据库设置

首先在Supabase创建新项目，然后运行数据库设置脚本：

```bash
# 设置数据库架构
npm run db:setup

# 或者设置架构并加载测试数据
npm run db:setup-with-seed
```

### 5. 启动开发服务器

```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 📚 项目结构

```
src/
├── app/                    # Next.js 13+ App Router
│   ├── api/               # API路由
│   ├── apps/              # 应用相关页面
│   ├── activities/        # 活动相关页面
│   ├── auth/              # 认证相关页面
│   └── ...
├── components/            # React组件
│   ├── auth/              # 认证组件
│   ├── apps/              # 应用组件
│   ├── activities/        # 活动组件
│   └── layout/            # 布局组件
├── lib/                   # 工具库
│   ├── supabase/          # Supabase客户端
│   ├── validations/       # 数据验证
│   └── utils/             # 工具函数
└── types/                 # TypeScript类型定义
```

## 🗄️ 数据库架构

### 核心表结构

- **users**: 用户信息表（扩展auth.users）
- **apps**: 应用信息表
- **activities**: 送码活动表
- **code_claims**: 邀请码领取记录表
- **user_subscriptions**: 用户应用订阅表

### 关键功能

- **热度算法**: `热度分数 = 总活动数 + (最近30天活动数 × 2)`
- **自动触发器**: 用户注册时自动创建用户记录
- **RLS策略**: 行级安全策略保护数据访问
- **索引优化**: 针对查询性能优化的索引

## 🔧 开发指南

### 数据库管理

```bash
# 重置数据库（删除所有数据和表）
npm run db:reset

# 仅加载种子数据
npm run db:seed

# 设置新的数据库架构
npm run db:setup
```

### 代码规范

- 使用TypeScript进行类型检查
- 使用Zod进行数据验证
- 遵循Next.js最佳实践
- 使用Tailwind CSS进行样式设计

### API设计

- RESTful API设计
- 统一的错误处理
- 输入数据验证
- 权限检查中间件

## 🚀 部署指南

### Vercel部署（推荐）

1. 将代码推送到GitHub
2. 在Vercel中导入项目
3. 配置环境变量
4. 部署

### 环境变量配置

生产环境需要配置以下环境变量：

```bash
NEXT_PUBLIC_SUPABASE_URL=your_production_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_production_service_role_key
NEXT_PUBLIC_APP_URL=https://your-domain.com
```

### 数据库迁移

在生产环境中运行数据库设置：

```bash
npm run db:setup
```

**注意**: 不要在生产环境运行 `db:setup-with-seed`，这会加载测试数据。

## 🔒 安全考虑

- 使用Supabase RLS策略保护数据
- 输入数据验证和清理
- CSRF保护
- 安全的密码处理
- OAuth安全配置

## 📈 性能优化

- Next.js自动代码分割
- 图片优化
- 数据库查询优化
- 缓存策略
- CDN配置

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- 项目链接: [GitHub Repository](https://github.com/your-username/app-recommendation-site)
- 问题反馈: [Issues](https://github.com/your-username/app-recommendation-site/issues)
- 邮箱: <EMAIL>

## 🙏 致谢

感谢以下开源项目：

- [Next.js](https://nextjs.org/)
- [Supabase](https://supabase.com/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Heroicons](https://heroicons.com/)
- [React Hook Form](https://react-hook-form.com/)
- [Zod](https://zod.dev/)
