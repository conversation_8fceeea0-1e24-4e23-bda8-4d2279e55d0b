# 部署指南

本文档详细说明如何将应用推荐网站部署到生产环境。

## 🚀 部署选项

### 1. Vercel部署（推荐）

Vercel是Next.js的官方部署平台，提供最佳的性能和开发体验。

#### 步骤：

1. **准备代码**
   ```bash
   git add .
   git commit -m "Ready for deployment"
   git push origin main
   ```

2. **创建Vercel项目**
   - 访问 [vercel.com](https://vercel.com)
   - 点击"New Project"
   - 导入GitHub仓库
   - 选择框架预设：Next.js

3. **配置环境变量**
   在Vercel项目设置中添加以下环境变量：
   ```
   NEXT_PUBLIC_SUPABASE_URL=your_production_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_production_service_role_key
   NEXT_PUBLIC_APP_URL=https://your-domain.vercel.app
   ```

4. **部署**
   - 点击"Deploy"
   - 等待构建完成
   - 访问生成的URL验证部署

#### 自动部署

配置完成后，每次推送到main分支都会自动触发部署。

### 2. 其他平台部署

#### Netlify

1. 连接GitHub仓库
2. 设置构建命令：`npm run build`
3. 设置发布目录：`.next`
4. 配置环境变量

#### Railway

1. 连接GitHub仓库
2. 选择Next.js模板
3. 配置环境变量
4. 部署

## 🗄️ 数据库设置

### Supabase生产环境

1. **创建生产项目**
   - 在Supabase控制台创建新项目
   - 选择合适的区域（建议选择离用户最近的区域）
   - 设置强密码

2. **配置数据库**
   ```bash
   # 设置环境变量后运行
   npm run db:setup
   ```

3. **配置RLS策略**
   - 在Supabase控制台中启用RLS
   - 验证所有策略正确应用

4. **配置认证**
   - 设置认证提供商（Google、GitHub、Discord）
   - 配置重定向URL
   - 设置邮件模板

### 数据库迁移

如果需要更新数据库架构：

```bash
# 备份现有数据
pg_dump your_database_url > backup.sql

# 运行迁移
npm run db:setup

# 如果需要，恢复数据
psql your_database_url < backup.sql
```

## 🔧 环境配置

### 必需的环境变量

```bash
# Supabase配置
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# 应用配置
NEXT_PUBLIC_APP_URL=https://your-domain.com
NODE_ENV=production
```

### 可选的环境变量

```bash
# OAuth配置
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# 监控配置
SENTRY_DSN=your_sentry_dsn
VERCEL_ANALYTICS_ID=your_analytics_id

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
```

## 🔍 部署验证

### 自动化部署脚本

使用提供的部署脚本：

```bash
# 开发环境构建测试
npm run deploy

# 生产环境部署
npm run deploy:prod
```

### 手动验证步骤

1. **健康检查**
   ```bash
   curl https://your-domain.com/api/health
   ```

2. **功能测试**
   - 用户注册/登录
   - 应用发布
   - 活动创建
   - 邀请码领取

3. **性能测试**
   - 页面加载速度
   - API响应时间
   - 数据库查询性能

## 🔒 安全配置

### 1. 环境变量安全

- 不要在代码中硬编码敏感信息
- 使用平台的环境变量管理
- 定期轮换密钥

### 2. 数据库安全

- 启用RLS策略
- 使用最小权限原则
- 定期备份数据
- 监控异常访问

### 3. 应用安全

- 启用HTTPS
- 配置安全头
- 输入验证和清理
- 防止SQL注入和XSS

## 📊 监控和日志

### 1. 应用监控

推荐使用以下工具：

- **Vercel Analytics**: 性能监控
- **Sentry**: 错误追踪
- **LogRocket**: 用户会话录制
- **Supabase Dashboard**: 数据库监控

### 2. 关键指标

监控以下指标：

- 页面加载时间
- API响应时间
- 错误率
- 用户活跃度
- 数据库性能

### 3. 告警设置

设置以下告警：

- 应用宕机
- 错误率过高
- 响应时间过长
- 数据库连接失败

## 🔄 CI/CD流程

### GitHub Actions示例

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run type-check
      - run: npm run lint
      - run: npm run build
      - uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
```

## 🚨 故障排除

### 常见问题

1. **构建失败**
   - 检查TypeScript错误
   - 验证环境变量
   - 查看构建日志

2. **数据库连接失败**
   - 验证Supabase URL和密钥
   - 检查网络连接
   - 确认RLS策略

3. **认证问题**
   - 检查OAuth配置
   - 验证重定向URL
   - 确认域名设置

### 调试工具

- Vercel函数日志
- Supabase日志
- 浏览器开发者工具
- 网络监控工具

## 📞 支持

如果遇到部署问题：

1. 查看项目文档
2. 检查GitHub Issues
3. 联系技术支持
4. 参考社区论坛

---

**注意**: 部署到生产环境前，请确保在测试环境中充分验证所有功能。
