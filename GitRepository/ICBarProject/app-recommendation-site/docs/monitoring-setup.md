# 监控和告警配置指南

本文档说明如何为应用推荐网站配置监控和告警系统。

## 🔍 监控指标

### 应用性能指标
- **响应时间**: API端点响应时间
- **吞吐量**: 每秒请求数(RPS)
- **错误率**: 4xx和5xx错误的百分比
- **可用性**: 应用正常运行时间百分比

### 业务指标
- **用户注册数**: 每日新用户注册数量
- **应用发布数**: 每日新应用发布数量
- **活动创建数**: 每日新活动创建数量
- **邀请码领取数**: 每日邀请码领取数量
- **VIP转化率**: VIP用户转化百分比

### 基础设施指标
- **CPU使用率**: 服务器CPU使用百分比
- **内存使用率**: 服务器内存使用百分比
- **数据库连接数**: 活跃数据库连接数
- **存储使用量**: 数据库和文件存储使用量

## 📊 监控工具配置

### 1. Vercel Analytics（推荐）

Vercel提供内置的分析功能，适合Next.js应用。

```bash
# 安装Vercel Analytics
npm install @vercel/analytics

# 在应用中集成
# src/app/layout.tsx
import { Analytics } from '@vercel/analytics/react'

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        {children}
        <Analytics />
      </body>
    </html>
  )
}
```

### 2. Google Analytics 4

配置GA4进行用户行为分析。

```javascript
// src/lib/analytics/ga4.ts
export const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GA_ID

export const pageview = (url: string) => {
  window.gtag('config', GA_TRACKING_ID, {
    page_path: url,
  })
}

export const event = ({ action, category, label, value }) => {
  window.gtag('event', action, {
    event_category: category,
    event_label: label,
    value: value,
  })
}
```

### 3. Sentry错误监控

配置Sentry进行错误追踪。

```bash
# 安装Sentry
npm install @sentry/nextjs

# 配置文件 sentry.client.config.js
import * as Sentry from '@sentry/nextjs'

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  tracesSampleRate: 1.0,
})
```

### 4. 自定义监控API

使用我们已创建的监控API收集自定义指标。

```typescript
// 发送自定义指标
import { performanceMonitor } from '@/lib/monitoring/performance'

// 记录业务指标
performanceMonitor.recordMetric('user_registration', 1, {
  source: 'email',
  timestamp: Date.now()
})

// 记录API性能
performanceMonitor.recordApiCall('/api/apps', 'GET', 250, 200)
```

## 🚨 告警配置

### 1. Vercel监控告警

在Vercel控制台配置告警规则：

- **响应时间告警**: 平均响应时间 > 2秒
- **错误率告警**: 错误率 > 5%
- **可用性告警**: 可用性 < 99%

### 2. 自定义告警脚本

```bash
#!/bin/bash
# scripts/health-monitor.sh

APP_URL="${NEXT_PUBLIC_APP_URL}"
WEBHOOK_URL="${SLACK_WEBHOOK_URL}"

check_health() {
    response=$(curl -s -w "%{http_code}" "$APP_URL/api/health")
    http_code="${response: -3}"
    
    if [ "$http_code" != "200" ]; then
        send_alert "🚨 应用健康检查失败 (HTTP $http_code)"
        return 1
    fi
    
    return 0
}

send_alert() {
    message="$1"
    curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"$message\"}" \
        "$WEBHOOK_URL"
}

# 运行检查
if ! check_health; then
    exit 1
fi
```

### 3. 数据库监控

监控Supabase数据库性能：

```sql
-- 监控慢查询
SELECT 
    query,
    mean_exec_time,
    calls,
    total_exec_time
FROM pg_stat_statements 
WHERE mean_exec_time > 1000
ORDER BY mean_exec_time DESC;

-- 监控连接数
SELECT count(*) as active_connections 
FROM pg_stat_activity 
WHERE state = 'active';

-- 监控数据库大小
SELECT 
    pg_size_pretty(pg_database_size(current_database())) as db_size;
```

## 📈 仪表板配置

### 1. Grafana仪表板

如果使用Grafana，可以创建自定义仪表板：

```json
{
  "dashboard": {
    "title": "应用推荐网站监控",
    "panels": [
      {
        "title": "API响应时间",
        "type": "graph",
        "targets": [
          {
            "expr": "avg(api_response_time_ms)",
            "legendFormat": "平均响应时间"
          }
        ]
      },
      {
        "title": "用户注册趋势",
        "type": "graph",
        "targets": [
          {
            "expr": "sum(user_registrations_total)",
            "legendFormat": "注册用户数"
          }
        ]
      }
    ]
  }
}
```

### 2. 简单的监控页面

创建内部监控页面：

```typescript
// src/app/admin/monitoring/page.tsx
export default function MonitoringPage() {
  const [metrics, setMetrics] = useState(null)
  
  useEffect(() => {
    const fetchMetrics = async () => {
      const response = await fetch('/api/monitoring/dashboard')
      const data = await response.json()
      setMetrics(data)
    }
    
    fetchMetrics()
    const interval = setInterval(fetchMetrics, 30000) // 30秒刷新
    
    return () => clearInterval(interval)
  }, [])
  
  return (
    <div className="monitoring-dashboard">
      <h1>系统监控</h1>
      {metrics && (
        <div className="metrics-grid">
          <MetricCard 
            title="API响应时间" 
            value={`${metrics.avgResponseTime}ms`}
            status={metrics.avgResponseTime < 500 ? 'good' : 'warning'}
          />
          <MetricCard 
            title="错误率" 
            value={`${metrics.errorRate}%`}
            status={metrics.errorRate < 1 ? 'good' : 'error'}
          />
          <MetricCard 
            title="活跃用户" 
            value={metrics.activeUsers}
            status="info"
          />
        </div>
      )}
    </div>
  )
}
```

## 🔧 监控最佳实践

### 1. 监控层次
- **基础设施监控**: 服务器、网络、存储
- **应用监控**: 性能、错误、可用性
- **业务监控**: 用户行为、转化率、收入

### 2. 告警策略
- **分级告警**: 严重、警告、信息
- **告警抑制**: 避免告警风暴
- **告警升级**: 未处理告警的升级机制

### 3. 监控数据保留
- **实时数据**: 保留7天
- **小时聚合**: 保留30天
- **日聚合**: 保留1年

### 4. 性能基准
- **页面加载时间**: < 3秒
- **API响应时间**: < 500ms
- **可用性**: > 99.9%
- **错误率**: < 1%

## 📞 事件响应

### 1. 告警响应流程
1. **接收告警**: 通过邮件、短信、Slack等
2. **初步评估**: 确定影响范围和严重程度
3. **问题诊断**: 使用监控数据定位问题
4. **问题修复**: 实施修复措施
5. **验证修复**: 确认问题已解决
6. **事后总结**: 分析原因并改进

### 2. 联系人配置
```yaml
# 告警联系人配置
contacts:
  primary:
    - name: "技术负责人"
      email: "<EMAIL>"
      phone: "+86-xxx-xxxx-xxxx"
  
  secondary:
    - name: "运维工程师"
      email: "<EMAIL>"
      phone: "+86-xxx-xxxx-xxxx"
```

### 3. 升级策略
- **5分钟**: 发送给主要联系人
- **15分钟**: 发送给次要联系人
- **30分钟**: 发送给管理层

## 📋 监控检查清单

### 部署前
- [ ] 监控工具已配置
- [ ] 告警规则已设置
- [ ] 联系人信息已更新
- [ ] 仪表板已创建

### 部署后
- [ ] 监控数据正常收集
- [ ] 告警测试通过
- [ ] 基准指标已建立
- [ ] 团队已培训

### 日常维护
- [ ] 定期检查监控数据
- [ ] 更新告警阈值
- [ ] 清理过期数据
- [ ] 优化监控性能

---

**注意**: 监控系统本身也需要监控，确保监控工具的可用性和准确性。
