# 数据库设置指南

## 概述

本项目使用Supabase作为后端数据库服务。本指南将帮助您设置和配置数据库。

## 前置要求

1. **Supabase账号**: 在 [supabase.com](https://supabase.com) 创建账号
2. **Node.js**: 确保安装了Node.js 18+
3. **项目依赖**: 运行 `npm install` 安装所有依赖

## 步骤1: 创建Supabase项目

1. 登录Supabase控制台
2. 点击"New Project"创建新项目
3. 选择组织和区域
4. 设置数据库密码
5. 等待项目创建完成

## 步骤2: 获取项目配置

在Supabase项目控制台中：

1. 进入 **Settings** > **API**
2. 复制以下信息：
   - Project URL
   - anon public key
   - service_role key (仅用于服务端)

## 步骤3: 配置环境变量

1. 复制 `.env.local.example` 为 `.env.local`
2. 填入Supabase配置信息：

```bash
# Supabase配置
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# 应用配置
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## 步骤4: 设置数据库架构

运行以下命令之一：

```bash
# 仅设置数据库架构
npm run db:setup

# 设置架构并加载测试数据
npm run db:setup-with-seed
```

## 步骤5: 验证设置

1. 在Supabase控制台中检查表是否创建成功
2. 验证RLS策略是否正确应用
3. 运行开发服务器测试连接：

```bash
npm run dev
```

## 数据库架构说明

### 核心表结构

- **users**: 用户信息表（扩展auth.users）
- **apps**: 应用信息表
- **activities**: 送码活动表
- **code_claims**: 邀请码领取记录表
- **user_subscriptions**: 用户应用订阅表

### 关键功能

- **自动触发器**: 用户注册时自动创建用户记录
- **热度计算**: 活动创建时自动更新应用热度分数
- **RLS策略**: 行级安全策略保护数据访问
- **索引优化**: 针对查询性能优化的索引

## 常用数据库命令

```bash
# 重置数据库（删除所有数据和表）
npm run db:reset

# 仅加载种子数据
npm run db:seed

# 设置新的数据库架构
npm run db:setup
```

## 开发数据

运行 `npm run db:setup-with-seed` 会创建以下测试数据：

- 5个示例应用（不同分类）
- 5个送码活动（不同条件设置）
- 4个测试用户（包含VIP用户）
- 一些邀请码领取记录
- 用户订阅关系

## 故障排除

### 连接问题

1. 检查环境变量是否正确设置
2. 确认Supabase项目状态正常
3. 验证API密钥权限

### 权限问题

1. 确保使用service_role密钥进行架构设置
2. 检查RLS策略配置
3. 验证用户认证状态

### 数据问题

1. 检查外键约束
2. 验证数据格式
3. 查看Supabase日志

## 生产环境注意事项

1. **不要在生产环境运行种子数据**
2. **定期备份数据库**
3. **监控数据库性能**
4. **定期更新RLS策略**
5. **使用环境变量管理敏感信息**

## 相关文档

- [Supabase文档](https://supabase.com/docs)
- [PostgreSQL文档](https://www.postgresql.org/docs/)
- [Next.js + Supabase指南](https://supabase.com/docs/guides/getting-started/quickstarts/nextjs)
