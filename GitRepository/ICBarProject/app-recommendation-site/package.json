{"name": "app-recommendation-site", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "db:setup": "node scripts/setup-database.js setup", "db:setup-with-seed": "node scripts/setup-database.js setup --seed", "db:reset": "node scripts/setup-database.js reset", "db:seed": "node scripts/setup-database.js seed", "deploy": "bash scripts/deploy.sh", "deploy:prod": "bash scripts/deploy.sh --prod", "health-check": "curl -f http://localhost:3000/api/health || echo 'Health check failed'", "post-deploy-check": "bash scripts/post-deploy-check.sh", "analyze": "cross-env ANALYZE=true next build", "lighthouse": "lighthouse http://localhost:3000 --output html --output-path ./reports/lighthouse.html"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "lucide-react": "^0.515.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.0", "zod": "^3.25.64"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/jest": "^29.5.0", "@testing-library/react": "^14.0.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/user-event": "^14.0.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "cross-env": "^7.0.3", "lighthouse": "^11.0.0", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}