-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 用户表（扩展auth.users）
CREATE TABLE public.users (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT NOT NULL,
  username TEXT UNIQUE,
  full_name TEXT,
  avatar_url TEXT,
  is_verified BOOLEAN DEFAULT FALSE,
  is_vip BOOLEAN DEFAULT FALSE,
  vip_expires_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 应用表
CREATE TABLE public.apps (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  icon_url TEXT,
  category TEXT NOT NULL,
  developer TEXT NOT NULL,
  website_url TEXT,
  download_url TEXT,
  created_by <PERSON><PERSON><PERSON> REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  activity_count INTEGER DEFAULT 0,
  hot_score DECIMAL DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 送码活动表
CREATE TABLE public.activities (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  app_id UUID REFERENCES public.apps(id) ON DELETE CASCADE NOT NULL,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  invite_codes TEXT[] NOT NULL,
  total_codes INTEGER NOT NULL,
  used_codes INTEGER DEFAULT 0,
  created_by UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  requires_verification BOOLEAN DEFAULT FALSE,
  requires_subscription BOOLEAN DEFAULT FALSE,
  required_apps UUID[],
  feedback_required BOOLEAN DEFAULT FALSE,
  feedback_questions TEXT[],
  is_active BOOLEAN DEFAULT TRUE,
  expires_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 邀请码领取记录表
CREATE TABLE public.code_claims (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  activity_id UUID REFERENCES public.activities(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  invite_code TEXT NOT NULL,
  claimed_at TIMESTAMPTZ DEFAULT NOW(),
  feedback_submitted BOOLEAN DEFAULT FALSE,
  feedback_data JSONB,
  UNIQUE(activity_id, user_id)
);

-- 用户应用订阅表
CREATE TABLE public.user_subscriptions (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  app_id UUID REFERENCES public.apps(id) ON DELETE CASCADE NOT NULL,
  subscribed_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, app_id)
);

-- 创建索引
CREATE INDEX idx_apps_category ON public.apps(category);
CREATE INDEX idx_apps_hot_score ON public.apps(hot_score DESC);
CREATE INDEX idx_apps_created_by ON public.apps(created_by);
CREATE INDEX idx_activities_app_id ON public.activities(app_id);
CREATE INDEX idx_activities_created_by ON public.activities(created_by);
CREATE INDEX idx_activities_is_active ON public.activities(is_active);
CREATE INDEX idx_code_claims_activity_id ON public.code_claims(activity_id);
CREATE INDEX idx_code_claims_user_id ON public.code_claims(user_id);
CREATE INDEX idx_user_subscriptions_user_id ON public.user_subscriptions(user_id);
CREATE INDEX idx_user_subscriptions_app_id ON public.user_subscriptions(app_id);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表添加更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_apps_updated_at BEFORE UPDATE ON public.apps
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_activities_updated_at BEFORE UPDATE ON public.activities
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建用户注册时自动创建用户记录的函数
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, full_name, avatar_url)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'avatar_url');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建触发器
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 更新应用热度分数的函数
CREATE OR REPLACE FUNCTION update_app_hot_score(app_uuid UUID)
RETURNS VOID AS $$
DECLARE
  activity_count INTEGER;
  recent_activity_count INTEGER;
  hot_score DECIMAL;
BEGIN
  -- 计算总活动数
  SELECT COUNT(*) INTO activity_count
  FROM public.activities
  WHERE app_id = app_uuid;
  
  -- 计算最近30天的活动数
  SELECT COUNT(*) INTO recent_activity_count
  FROM public.activities
  WHERE app_id = app_uuid
    AND created_at >= NOW() - INTERVAL '30 days';
  
  -- 计算热度分数（最近活动权重更高）
  hot_score := activity_count + (recent_activity_count * 2);
  
  -- 更新应用记录
  UPDATE public.apps
  SET activity_count = activity_count,
      hot_score = hot_score,
      updated_at = NOW()
  WHERE id = app_uuid;
END;
$$ LANGUAGE plpgsql;

-- 创建活动时自动更新应用热度的触发器
CREATE OR REPLACE FUNCTION trigger_update_app_hot_score()
RETURNS TRIGGER AS $$
BEGIN
  PERFORM update_app_hot_score(NEW.app_id);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER on_activity_created
  AFTER INSERT ON public.activities
  FOR EACH ROW EXECUTE FUNCTION trigger_update_app_hot_score();

-- 设置行级安全策略（RLS）
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.apps ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.code_claims ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_subscriptions ENABLE ROW LEVEL SECURITY;

-- 用户表策略
CREATE POLICY "Users can view all profiles" ON public.users FOR SELECT USING (true);
CREATE POLICY "Users can update own profile" ON public.users FOR UPDATE USING (auth.uid() = id);

-- 应用表策略
CREATE POLICY "Anyone can view apps" ON public.apps FOR SELECT USING (true);
CREATE POLICY "Authenticated users can create apps" ON public.apps FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Users can update own apps" ON public.apps FOR UPDATE USING (auth.uid() = created_by);
CREATE POLICY "Users can delete own apps" ON public.apps FOR DELETE USING (auth.uid() = created_by);

-- 活动表策略
CREATE POLICY "Anyone can view active activities" ON public.activities FOR SELECT USING (is_active = true);
CREATE POLICY "Authenticated users can create activities" ON public.activities FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Users can update own activities" ON public.activities FOR UPDATE USING (auth.uid() = created_by);
CREATE POLICY "Users can delete own activities" ON public.activities FOR DELETE USING (auth.uid() = created_by);

-- 邀请码领取记录策略
CREATE POLICY "Users can view own claims" ON public.code_claims FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create own claims" ON public.code_claims FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own claims" ON public.code_claims FOR UPDATE USING (auth.uid() = user_id);

-- 用户订阅策略
CREATE POLICY "Users can view own subscriptions" ON public.user_subscriptions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own subscriptions" ON public.user_subscriptions FOR ALL USING (auth.uid() = user_id);
