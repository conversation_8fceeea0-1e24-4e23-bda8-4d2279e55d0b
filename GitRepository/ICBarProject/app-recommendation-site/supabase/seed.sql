-- 种子数据文件
-- 注意：这个文件仅用于开发和测试环境

-- 插入测试用户（需要先在Supabase Auth中创建对应的用户）
-- 这里假设已经有一些测试用户ID

-- 示例应用数据
INSERT INTO public.apps (id, name, description, icon_url, category, developer, website_url, download_url, created_by) VALUES
(
  '550e8400-e29b-41d4-a716-446655440001',
  'TaskMaster Pro',
  '专业的任务管理工具，帮助您高效组织工作和生活。支持项目管理、时间跟踪、团队协作等功能。',
  'https://example.com/icons/taskmaster.png',
  'productivity',
  'ProductivityCorp',
  'https://taskmaster.example.com',
  'https://apps.apple.com/app/taskmaster',
  '00000000-0000-0000-0000-000000000001'
),
(
  '550e8400-e29b-41d4-a716-446655440002',
  'SocialHub',
  '全新的社交网络平台，连接志同道合的朋友。支持动态分享、群组聊天、活动组织等功能。',
  'https://example.com/icons/socialhub.png',
  'social',
  'SocialTech Inc',
  'https://socialhub.example.com',
  'https://play.google.com/store/apps/details?id=com.socialhub',
  '00000000-0000-0000-0000-000000000001'
),
(
  '550e8400-e29b-41d4-a716-446655440003',
  'GameZone',
  '精品游戏平台，汇集最新最热门的手机游戏。支持游戏推荐、成就系统、好友对战等功能。',
  'https://example.com/icons/gamezone.png',
  'entertainment',
  'GameStudio',
  'https://gamezone.example.com',
  'https://gamezone.example.com/download',
  '00000000-0000-0000-0000-000000000002'
),
(
  '550e8400-e29b-41d4-a716-446655440004',
  'LearnSmart',
  '智能学习助手，个性化定制学习计划。支持多学科内容、进度跟踪、智能推荐等功能。',
  'https://example.com/icons/learnsmart.png',
  'education',
  'EduTech Solutions',
  'https://learnsmart.example.com',
  'https://learnsmart.example.com/app',
  '00000000-0000-0000-0000-000000000002'
),
(
  '550e8400-e29b-41d4-a716-446655440005',
  'MoneyTracker',
  '专业的个人财务管理工具，帮助您掌控财务状况。支持记账、预算、投资跟踪等功能。',
  'https://example.com/icons/moneytracker.png',
  'finance',
  'FinanceApp Ltd',
  'https://moneytracker.example.com',
  'https://apps.apple.com/app/moneytracker',
  '00000000-0000-0000-0000-000000000003'
);

-- 示例送码活动数据
INSERT INTO public.activities (id, app_id, title, description, invite_codes, total_codes, created_by, requires_verification, requires_subscription, feedback_required, feedback_questions, expires_at) VALUES
(
  '660e8400-e29b-41d4-a716-446655440001',
  '550e8400-e29b-41d4-a716-446655440001',
  'TaskMaster Pro 新用户专享',
  '欢迎新用户体验TaskMaster Pro！获取邀请码即可享受3个月免费高级功能。',
  ARRAY['TASK2024A1', 'TASK2024A2', 'TASK2024A3', 'TASK2024A4', 'TASK2024A5'],
  5,
  '00000000-0000-0000-0000-000000000001',
  false,
  false,
  false,
  NULL,
  NOW() + INTERVAL '30 days'
),
(
  '660e8400-e29b-41d4-a716-446655440002',
  '550e8400-e29b-41d4-a716-446655440002',
  'SocialHub VIP会员招募',
  '限时VIP会员招募活动！通过邀请码注册即可获得6个月VIP会员权益。',
  ARRAY['SOCIAL2024B1', 'SOCIAL2024B2', 'SOCIAL2024B3'],
  3,
  '00000000-0000-0000-0000-000000000001',
  true,
  false,
  true,
  ARRAY['您对我们的社交功能有什么建议？', '您最希望增加哪些新功能？'],
  NOW() + INTERVAL '15 days'
),
(
  '660e8400-e29b-41d4-a716-446655440003',
  '550e8400-e29b-41d4-a716-446655440003',
  'GameZone 内测邀请',
  '独家内测邀请！成为首批体验者，获得限定游戏道具和特殊称号。',
  ARRAY['GAME2024C1', 'GAME2024C2', 'GAME2024C3', 'GAME2024C4', 'GAME2024C5', 'GAME2024C6', 'GAME2024C7', 'GAME2024C8', 'GAME2024C9', 'GAME2024C10'],
  10,
  '00000000-0000-0000-0000-000000000002',
  false,
  true,
  false,
  NULL,
  NOW() + INTERVAL '7 days'
),
(
  '660e8400-e29b-41d4-a716-446655440004',
  '550e8400-e29b-41d4-a716-446655440004',
  'LearnSmart 学生专享',
  '学生专享优惠活动！验证学生身份即可获得一年免费使用权限。',
  ARRAY['LEARN2024D1', 'LEARN2024D2', 'LEARN2024D3', 'LEARN2024D4', 'LEARN2024D5', 'LEARN2024D6', 'LEARN2024D7', 'LEARN2024D8', 'LEARN2024D9', 'LEARN2024D10', 'LEARN2024D11', 'LEARN2024D12', 'LEARN2024D13', 'LEARN2024D14', 'LEARN2024D15'],
  15,
  '00000000-0000-0000-0000-000000000002',
  true,
  false,
  true,
  ARRAY['您的专业领域是什么？', '您希望学习哪些新技能？', '您对我们的课程内容有什么建议？'],
  NOW() + INTERVAL '60 days'
),
(
  '660e8400-e29b-41d4-a716-446655440005',
  '550e8400-e29b-41d4-a716-446655440005',
  'MoneyTracker 理财达人招募',
  '理财达人招募中！分享您的理财心得，获得高级功能永久使用权。',
  ARRAY['MONEY2024E1', 'MONEY2024E2', 'MONEY2024E3', 'MONEY2024E4', 'MONEY2024E5', 'MONEY2024E6', 'MONEY2024E7'],
  7,
  '00000000-0000-0000-0000-000000000003',
  true,
  true,
  true,
  ARRAY['您的理财经验有多少年？', '您最常使用哪些理财工具？', '您希望我们增加哪些理财功能？', '您对我们的界面设计有什么建议？'],
  NOW() + INTERVAL '45 days'
);

-- 模拟一些邀请码领取记录
INSERT INTO public.code_claims (activity_id, user_id, invite_code, feedback_submitted, feedback_data) VALUES
(
  '660e8400-e29b-41d4-a716-446655440001',
  '00000000-0000-0000-0000-000000000002',
  'TASK2024A1',
  false,
  NULL
),
(
  '660e8400-e29b-41d4-a716-446655440002',
  '00000000-0000-0000-0000-000000000003',
  'SOCIAL2024B1',
  true,
  '{"question1": "界面很清爽，希望增加暗色主题", "question2": "希望增加语音聊天功能"}'::jsonb
),
(
  '660e8400-e29b-41d4-a716-446655440003',
  '00000000-0000-0000-0000-000000000001',
  'GAME2024C1',
  false,
  NULL
),
(
  '660e8400-e29b-41d4-a716-446655440003',
  '00000000-0000-0000-0000-000000000002',
  'GAME2024C2',
  false,
  NULL
);

-- 模拟一些用户订阅
INSERT INTO public.user_subscriptions (user_id, app_id) VALUES
('00000000-0000-0000-0000-000000000001', '550e8400-e29b-41d4-a716-446655440001'),
('00000000-0000-0000-0000-000000000001', '550e8400-e29b-41d4-a716-446655440002'),
('00000000-0000-0000-0000-000000000001', '550e8400-e29b-41d4-a716-446655440003'),
('00000000-0000-0000-0000-000000000002', '550e8400-e29b-41d4-a716-446655440003'),
('00000000-0000-0000-0000-000000000002', '550e8400-e29b-41d4-a716-446655440004'),
('00000000-0000-0000-0000-000000000003', '550e8400-e29b-41d4-a716-446655440002'),
('00000000-0000-0000-0000-000000000003', '550e8400-e29b-41d4-a716-446655440005');

-- 更新活动的已使用邀请码数量
UPDATE public.activities SET used_codes = 1 WHERE id = '660e8400-e29b-41d4-a716-446655440001';
UPDATE public.activities SET used_codes = 1 WHERE id = '660e8400-e29b-41d4-a716-446655440002';
UPDATE public.activities SET used_codes = 2 WHERE id = '660e8400-e29b-41d4-a716-446655440003';

-- 更新应用的热度分数（触发器会自动计算，这里手动设置一些初始值）
UPDATE public.apps SET activity_count = 1, hot_score = 3.0 WHERE id = '550e8400-e29b-41d4-a716-446655440001';
UPDATE public.apps SET activity_count = 1, hot_score = 5.0 WHERE id = '550e8400-e29b-41d4-a716-446655440002';
UPDATE public.apps SET activity_count = 1, hot_score = 4.0 WHERE id = '550e8400-e29b-41d4-a716-446655440003';
UPDATE public.apps SET activity_count = 1, hot_score = 3.5 WHERE id = '550e8400-e29b-41d4-a716-446655440004';
UPDATE public.apps SET activity_count = 1, hot_score = 2.5 WHERE id = '550e8400-e29b-41d4-a716-446655440005';

-- 创建一些示例用户资料（假设这些用户ID已经在auth.users中存在）
INSERT INTO public.users (id, email, username, full_name, is_verified, is_vip, vip_expires_at) VALUES
('00000000-0000-0000-0000-000000000001', '<EMAIL>', 'admin', '管理员', true, true, NOW() + INTERVAL '1 year'),
('00000000-0000-0000-0000-000000000002', '<EMAIL>', 'user1', '张三', true, false, NULL),
('00000000-0000-0000-0000-000000000003', '<EMAIL>', 'user2', '李四', false, false, NULL),
('00000000-0000-0000-0000-000000000004', '<EMAIL>', 'vipuser', '王五', true, true, NOW() + INTERVAL '6 months')
ON CONFLICT (id) DO UPDATE SET
  username = EXCLUDED.username,
  full_name = EXCLUDED.full_name,
  is_verified = EXCLUDED.is_verified,
  is_vip = EXCLUDED.is_vip,
  vip_expires_at = EXCLUDED.vip_expires_at;
