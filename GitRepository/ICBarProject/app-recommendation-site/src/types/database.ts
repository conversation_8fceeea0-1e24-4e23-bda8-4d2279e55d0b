export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          username: string | null
          full_name: string | null
          avatar_url: string | null
          is_verified: boolean
          is_vip: boolean
          vip_expires_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          username?: string | null
          full_name?: string | null
          avatar_url?: string | null
          is_verified?: boolean
          is_vip?: boolean
          vip_expires_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          username?: string | null
          full_name?: string | null
          avatar_url?: string | null
          is_verified?: boolean
          is_vip?: boolean
          vip_expires_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      apps: {
        Row: {
          id: string
          name: string
          description: string
          icon_url: string | null
          category: string
          developer: string
          website_url: string | null
          download_url: string | null
          created_by: string
          activity_count: number
          hot_score: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description: string
          icon_url?: string | null
          category: string
          developer: string
          website_url?: string | null
          download_url?: string | null
          created_by: string
          activity_count?: number
          hot_score?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string
          icon_url?: string | null
          category?: string
          developer?: string
          website_url?: string | null
          download_url?: string | null
          created_by?: string
          activity_count?: number
          hot_score?: number
          created_at?: string
          updated_at?: string
        }
      }
      activities: {
        Row: {
          id: string
          app_id: string
          title: string
          description: string
          invite_codes: string[]
          total_codes: number
          used_codes: number
          created_by: string
          requires_verification: boolean
          requires_subscription: boolean
          required_apps: string[] | null
          feedback_required: boolean
          feedback_questions: string[] | null
          is_active: boolean
          expires_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          app_id: string
          title: string
          description: string
          invite_codes: string[]
          total_codes: number
          used_codes?: number
          created_by: string
          requires_verification?: boolean
          requires_subscription?: boolean
          required_apps?: string[] | null
          feedback_required?: boolean
          feedback_questions?: string[] | null
          is_active?: boolean
          expires_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          app_id?: string
          title?: string
          description?: string
          invite_codes?: string[]
          total_codes?: number
          used_codes?: number
          created_by?: string
          requires_verification?: boolean
          requires_subscription?: boolean
          required_apps?: string[] | null
          feedback_required?: boolean
          feedback_questions?: string[] | null
          is_active?: boolean
          expires_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      code_claims: {
        Row: {
          id: string
          activity_id: string
          user_id: string
          invite_code: string
          claimed_at: string
          feedback_submitted: boolean
          feedback_data: Record<string, any> | null
        }
        Insert: {
          id?: string
          activity_id: string
          user_id: string
          invite_code: string
          claimed_at?: string
          feedback_submitted?: boolean
          feedback_data?: Record<string, any> | null
        }
        Update: {
          id?: string
          activity_id?: string
          user_id?: string
          invite_code?: string
          claimed_at?: string
          feedback_submitted?: boolean
          feedback_data?: Record<string, any> | null
        }
      }
      user_subscriptions: {
        Row: {
          id: string
          user_id: string
          app_id: string
          subscribed_at: string
        }
        Insert: {
          id?: string
          user_id: string
          app_id: string
          subscribed_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          app_id?: string
          subscribed_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
