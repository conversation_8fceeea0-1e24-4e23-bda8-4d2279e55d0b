// 性能监控工具

export interface PerformanceMetric {
  name: string
  value: number
  timestamp: number
  url?: string
  userAgent?: string
}

export interface WebVitalsMetric {
  id: string
  name: 'CLS' | 'FID' | 'FCP' | 'LCP' | 'TTFB'
  value: number
  delta: number
  rating: 'good' | 'needs-improvement' | 'poor'
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private isEnabled: boolean

  constructor() {
    this.isEnabled = process.env.NODE_ENV === 'production'
  }

  // 记录性能指标
  recordMetric(name: string, value: number, additionalData?: Record<string, any>) {
    if (!this.isEnabled) return

    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
      ...additionalData
    }

    this.metrics.push(metric)
    this.sendMetric(metric)
  }

  // 记录Web Vitals指标
  recordWebVital(metric: WebVitalsMetric) {
    if (!this.isEnabled) return

    this.recordMetric(`web-vital-${metric.name.toLowerCase()}`, metric.value, {
      id: metric.id,
      delta: metric.delta,
      rating: metric.rating
    })
  }

  // 记录API响应时间
  recordApiCall(endpoint: string, method: string, duration: number, status: number) {
    this.recordMetric('api-call', duration, {
      endpoint,
      method,
      status,
      type: 'api-response-time'
    })
  }

  // 记录页面加载时间
  recordPageLoad(route: string, loadTime: number) {
    this.recordMetric('page-load', loadTime, {
      route,
      type: 'page-load-time'
    })
  }

  // 记录用户交互
  recordUserInteraction(action: string, element: string, duration?: number) {
    this.recordMetric('user-interaction', duration || 0, {
      action,
      element,
      type: 'user-interaction'
    })
  }

  // 发送指标到监控服务
  private async sendMetric(metric: PerformanceMetric) {
    try {
      // 在生产环境中，这里可以发送到实际的监控服务
      // 例如：Google Analytics, Sentry, DataDog等
      
      if (process.env.NODE_ENV === 'development') {
        console.log('Performance Metric:', metric)
      }

      // 示例：发送到自定义监控端点
      if (typeof window !== 'undefined' && navigator.sendBeacon) {
        const data = JSON.stringify(metric)
        navigator.sendBeacon('/api/monitoring/metrics', data)
      }
    } catch (error) {
      console.error('Failed to send performance metric:', error)
    }
  }

  // 获取性能摘要
  getPerformanceSummary() {
    const summary = {
      totalMetrics: this.metrics.length,
      averagePageLoad: 0,
      averageApiResponse: 0,
      webVitals: {
        cls: 0,
        fid: 0,
        fcp: 0,
        lcp: 0,
        ttfb: 0
      }
    }

    const pageLoadMetrics = this.metrics.filter(m => m.name === 'page-load')
    const apiMetrics = this.metrics.filter(m => m.name === 'api-call')

    if (pageLoadMetrics.length > 0) {
      summary.averagePageLoad = pageLoadMetrics.reduce((sum, m) => sum + m.value, 0) / pageLoadMetrics.length
    }

    if (apiMetrics.length > 0) {
      summary.averageApiResponse = apiMetrics.reduce((sum, m) => sum + m.value, 0) / apiMetrics.length
    }

    // 计算Web Vitals平均值
    Object.keys(summary.webVitals).forEach(vital => {
      const vitalMetrics = this.metrics.filter(m => m.name === `web-vital-${vital}`)
      if (vitalMetrics.length > 0) {
        summary.webVitals[vital as keyof typeof summary.webVitals] = 
          vitalMetrics.reduce((sum, m) => sum + m.value, 0) / vitalMetrics.length
      }
    })

    return summary
  }

  // 清除指标
  clearMetrics() {
    this.metrics = []
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor()

// API调用包装器，自动记录性能
export async function monitoredFetch(url: string, options?: RequestInit) {
  const startTime = performance.now()
  
  try {
    const response = await fetch(url, options)
    const endTime = performance.now()
    const duration = endTime - startTime

    performanceMonitor.recordApiCall(
      url,
      options?.method || 'GET',
      duration,
      response.status
    )

    return response
  } catch (error) {
    const endTime = performance.now()
    const duration = endTime - startTime

    performanceMonitor.recordApiCall(
      url,
      options?.method || 'GET',
      duration,
      0 // 错误状态
    )

    throw error
  }
}

// 页面性能监控Hook
export function usePagePerformance(route: string) {
  if (typeof window !== 'undefined') {
    const startTime = performance.now()

    window.addEventListener('load', () => {
      const loadTime = performance.now() - startTime
      performanceMonitor.recordPageLoad(route, loadTime)
    })
  }
}

// Web Vitals监控
export function reportWebVitals(metric: WebVitalsMetric) {
  performanceMonitor.recordWebVital(metric)
}
