// 性能优化工具

import { useEffect, useRef, useState } from 'react'

// 防抖Hook
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// 节流Hook
export function useThrottle<T>(value: T, limit: number): T {
  const [throttledValue, setThrottledValue] = useState<T>(value)
  const lastRan = useRef(Date.now())

  useEffect(() => {
    const handler = setTimeout(() => {
      if (Date.now() - lastRan.current >= limit) {
        setThrottledValue(value)
        lastRan.current = Date.now()
      }
    }, limit - (Date.now() - lastRan.current))

    return () => {
      clearTimeout(handler)
    }
  }, [value, limit])

  return throttledValue
}

// 懒加载Hook
export function useLazyLoad(ref: React.RefObject<Element>, options?: IntersectionObserverInit) {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const [hasLoaded, setHasLoaded] = useState(false)

  useEffect(() => {
    const element = ref.current
    if (!element || hasLoaded) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsIntersecting(true)
          setHasLoaded(true)
          observer.unobserve(element)
        }
      },
      {
        threshold: 0.1,
        ...options
      }
    )

    observer.observe(element)

    return () => {
      observer.unobserve(element)
    }
  }, [ref, options, hasLoaded])

  return { isIntersecting, hasLoaded }
}

// 虚拟滚动Hook
export function useVirtualScroll<T>(
  items: T[],
  itemHeight: number,
  containerHeight: number,
  overscan: number = 5
) {
  const [scrollTop, setScrollTop] = useState(0)

  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  )

  const visibleItems = items.slice(startIndex, endIndex + 1).map((item, index) => ({
    item,
    index: startIndex + index
  }))

  const totalHeight = items.length * itemHeight
  const offsetY = startIndex * itemHeight

  return {
    visibleItems,
    totalHeight,
    offsetY,
    setScrollTop
  }
}

// 图片懒加载组件
interface LazyImageProps {
  src: string
  alt: string
  className?: string
  placeholder?: string
  onLoad?: () => void
  onError?: () => void
}

export function LazyImage({ 
  src, 
  alt, 
  className = '', 
  placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PC9zdmc+',
  onLoad,
  onError
}: LazyImageProps) {
  const [imageSrc, setImageSrc] = useState(placeholder)
  const [isLoaded, setIsLoaded] = useState(false)
  const imgRef = useRef<HTMLImageElement>(null)
  const { isIntersecting } = useLazyLoad(imgRef)

  useEffect(() => {
    if (isIntersecting && !isLoaded) {
      const img = new Image()
      img.onload = () => {
        setImageSrc(src)
        setIsLoaded(true)
        onLoad?.()
      }
      img.onerror = () => {
        onError?.()
      }
      img.src = src
    }
  }, [isIntersecting, isLoaded, src, onLoad, onError])

  return (
    <img
      ref={imgRef}
      src={imageSrc}
      alt={alt}
      className={className}
      loading="lazy"
    />
  )
}

// 缓存工具
class SimpleCache<T> {
  private cache = new Map<string, { data: T; timestamp: number }>()
  private ttl: number

  constructor(ttlMinutes: number = 5) {
    this.ttl = ttlMinutes * 60 * 1000
  }

  set(key: string, data: T): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }

  get(key: string): T | null {
    const item = this.cache.get(key)
    if (!item) return null

    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key)
      return null
    }

    return item.data
  }

  clear(): void {
    this.cache.clear()
  }

  size(): number {
    return this.cache.size
  }
}

export const apiCache = new SimpleCache(5) // 5分钟缓存

// 带缓存的fetch
export async function cachedFetch<T>(
  url: string,
  options?: RequestInit,
  cacheKey?: string
): Promise<T> {
  const key = cacheKey || `${options?.method || 'GET'}-${url}`
  
  // 尝试从缓存获取
  const cached = apiCache.get(key)
  if (cached) {
    return cached as T
  }

  // 发起请求
  const response = await fetch(url, options)
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  const data = await response.json()
  
  // 缓存结果
  apiCache.set(key, data)
  
  return data
}

// 批量请求工具
export class BatchRequestManager {
  private batches = new Map<string, Promise<any>>()
  private batchSize: number
  private delay: number

  constructor(batchSize: number = 10, delayMs: number = 100) {
    this.batchSize = batchSize
    this.delay = delayMs
  }

  async batchRequest<T>(
    requests: Array<() => Promise<T>>,
    batchKey: string = 'default'
  ): Promise<T[]> {
    // 检查是否已有相同的批次在处理
    if (this.batches.has(batchKey)) {
      return this.batches.get(batchKey)
    }

    const batchPromise = this.processBatch(requests)
    this.batches.set(batchKey, batchPromise)

    try {
      const results = await batchPromise
      return results
    } finally {
      this.batches.delete(batchKey)
    }
  }

  private async processBatch<T>(requests: Array<() => Promise<T>>): Promise<T[]> {
    const results: T[] = []
    
    for (let i = 0; i < requests.length; i += this.batchSize) {
      const batch = requests.slice(i, i + this.batchSize)
      const batchResults = await Promise.all(batch.map(req => req()))
      results.push(...batchResults)
      
      // 在批次之间添加延迟
      if (i + this.batchSize < requests.length) {
        await new Promise(resolve => setTimeout(resolve, this.delay))
      }
    }
    
    return results
  }
}

export const batchManager = new BatchRequestManager()

// 性能监控装饰器
export function withPerformanceMonitoring<T extends (...args: any[]) => any>(
  fn: T,
  name: string
): T {
  return ((...args: any[]) => {
    const start = performance.now()
    
    try {
      const result = fn(...args)
      
      // 如果是Promise，监控异步执行时间
      if (result instanceof Promise) {
        return result.finally(() => {
          const end = performance.now()
          console.log(`${name} 执行时间: ${end - start}ms`)
        })
      }
      
      const end = performance.now()
      console.log(`${name} 执行时间: ${end - start}ms`)
      return result
    } catch (error) {
      const end = performance.now()
      console.error(`${name} 执行失败 (${end - start}ms):`, error)
      throw error
    }
  }) as T
}
