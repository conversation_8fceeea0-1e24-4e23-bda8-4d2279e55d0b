import { z } from 'zod'

// 用户相关验证
export const userProfileSchema = z.object({
  username: z.string()
    .min(3, '用户名至少3个字符')
    .max(20, '用户名最多20个字符')
    .regex(/^[a-zA-Z0-9_-]+$/, '用户名只能包含字母、数字、下划线和连字符')
    .optional(),
  full_name: z.string()
    .min(1, '姓名不能为空')
    .max(50, '姓名最多50个字符')
    .optional(),
  avatar_url: z.string().url('请输入有效的头像URL').optional()
})

// 应用相关验证
export const appSchema = z.object({
  name: z.string()
    .min(1, '应用名称不能为空')
    .max(100, '应用名称最多100个字符'),
  description: z.string()
    .min(10, '应用描述至少10个字符')
    .max(1000, '应用描述最多1000个字符'),
  icon_url: z.string().url('请输入有效的图标URL').optional(),
  category: z.string()
    .min(1, '请选择应用分类'),
  developer: z.string()
    .min(1, '开发者名称不能为空')
    .max(100, '开发者名称最多100个字符'),
  website_url: z.string().url('请输入有效的网站URL').optional(),
  download_url: z.string().url('请输入有效的下载URL').optional()
})

export const appUpdateSchema = appSchema.partial()

// 活动相关验证
export const activitySchema = z.object({
  app_id: z.string().uuid('请选择有效的应用'),
  title: z.string()
    .min(1, '活动标题不能为空')
    .max(200, '活动标题最多200个字符'),
  description: z.string()
    .min(10, '活动描述至少10个字符')
    .max(2000, '活动描述最多2000个字符'),
  invite_codes: z.array(z.string().min(1, '邀请码不能为空'))
    .min(1, '至少需要一个邀请码')
    .max(1000, '邀请码数量不能超过1000个'),
  requires_verification: z.boolean().default(false),
  requires_subscription: z.boolean().default(false),
  required_apps: z.array(z.string().uuid()).optional(),
  feedback_required: z.boolean().default(false),
  feedback_questions: z.array(z.string().min(1, '问题不能为空')).optional(),
  expires_at: z.string().datetime().optional()
}).refine(data => {
  // 如果需要反馈，必须提供问题
  if (data.feedback_required && (!data.feedback_questions || data.feedback_questions.length === 0)) {
    return false
  }
  return true
}, {
  message: '需要反馈时必须提供反馈问题',
  path: ['feedback_questions']
}).refine(data => {
  // 验证邀请码数量
  return data.invite_codes.length <= 1000
}, {
  message: '邀请码数量不能超过1000个',
  path: ['invite_codes']
}).refine(data => {
  // 验证邀请码唯一性
  const uniqueCodes = new Set(data.invite_codes)
  return uniqueCodes.size === data.invite_codes.length
}, {
  message: '邀请码不能重复',
  path: ['invite_codes']
})

export const activityUpdateSchema = activitySchema.partial().omit(['app_id'])

// 邀请码领取验证
export const claimCodeSchema = z.object({
  activity_id: z.string().uuid('无效的活动ID'),
  invite_code: z.string().min(1, '邀请码不能为空')
})

// 反馈提交验证
export const feedbackSchema = z.object({
  claim_id: z.string().uuid('无效的领取记录ID'),
  feedback_data: z.record(z.any()).refine(data => {
    // 确保反馈数据不为空
    return Object.keys(data).length > 0
  }, '反馈数据不能为空')
})

// 搜索和筛选验证
export const appSearchSchema = z.object({
  query: z.string().optional(),
  category: z.string().optional(),
  sort_by: z.enum(['hot_score', 'created_at', 'activity_count']).default('hot_score'),
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20)
})

export const activitySearchSchema = z.object({
  app_id: z.string().uuid().optional(),
  is_active: z.boolean().optional(),
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20)
})

// 管理员验证
export const adminActivityStatsSchema = z.object({
  activity_id: z.string().uuid('无效的活动ID')
})

// VIP功能验证
export const vipActivitySchema = activitySchema.extend({
  requires_verification: z.boolean(),
  requires_subscription: z.boolean(),
  required_apps: z.array(z.string().uuid()).max(10, '最多可要求订阅10个应用'),
  feedback_required: z.boolean(),
  feedback_questions: z.array(z.string().min(1).max(500, '问题最多500个字符')).max(20, '最多20个反馈问题')
})

// 实名认证验证（模拟）
export const verificationSchema = z.object({
  real_name: z.string()
    .min(2, '真实姓名至少2个字符')
    .max(20, '真实姓名最多20个字符'),
  id_number: z.string()
    .regex(/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, '请输入有效的身份证号码'),
  phone: z.string()
    .regex(/^1[3-9]\d{9}$/, '请输入有效的手机号码')
})

// 常用的验证函数
export function validateInviteCodes(codes: string[]): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  
  if (codes.length === 0) {
    errors.push('至少需要一个邀请码')
  }
  
  if (codes.length > 1000) {
    errors.push('邀请码数量不能超过1000个')
  }
  
  const uniqueCodes = new Set(codes)
  if (uniqueCodes.size !== codes.length) {
    errors.push('邀请码不能重复')
  }
  
  codes.forEach((code, index) => {
    if (!code.trim()) {
      errors.push(`第${index + 1}个邀请码不能为空`)
    }
    if (code.length > 100) {
      errors.push(`第${index + 1}个邀请码过长`)
    }
  })
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

export function validateActivityConditions(
  isVip: boolean,
  requiresVerification: boolean,
  requiresSubscription: boolean,
  requiredApps?: string[],
  feedbackRequired?: boolean,
  feedbackQuestions?: string[]
): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  
  // 非VIP用户不能设置高级条件
  if (!isVip) {
    if (requiresVerification) {
      errors.push('只有VIP用户可以设置实名认证要求')
    }
    if (requiresSubscription) {
      errors.push('只有VIP用户可以设置应用订阅要求')
    }
    if (feedbackRequired) {
      errors.push('只有VIP用户可以设置反馈要求')
    }
  }
  
  // 验证反馈问题
  if (feedbackRequired && (!feedbackQuestions || feedbackQuestions.length === 0)) {
    errors.push('需要反馈时必须提供反馈问题')
  }
  
  // 验证必需应用
  if (requiresSubscription && (!requiredApps || requiredApps.length === 0)) {
    errors.push('需要应用订阅时必须指定应用')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}
