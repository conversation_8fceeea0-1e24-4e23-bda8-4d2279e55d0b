import { createClient } from '@/lib/supabase/server'
import { createClient as createBrowserClient } from '@/lib/supabase/client'
import { redirect } from 'next/navigation'

// 服务端认证助手
export async function getUser() {
  const supabase = await createClient()
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser()
    
    if (error || !user) {
      return null
    }
    
    return user
  } catch (error) {
    return null
  }
}

export async function getUserProfile() {
  const user = await getUser()
  if (!user) return null
  
  const supabase = await createClient()
  
  try {
    const { data: profile, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single()
    
    if (error) {
      console.error('获取用户资料失败:', error)
      return null
    }
    
    return { user, profile }
  } catch (error) {
    console.error('获取用户资料异常:', error)
    return null
  }
}

export async function requireAuth() {
  const user = await getUser()
  if (!user) {
    redirect('/login')
  }
  return user
}

export async function requireVerification() {
  const userProfile = await getUserProfile()
  if (!userProfile) {
    redirect('/login')
  }
  
  if (!userProfile.profile.is_verified) {
    redirect('/verification')
  }
  
  return userProfile
}

export async function requireVip() {
  const userProfile = await getUserProfile()
  if (!userProfile) {
    redirect('/login')
  }
  
  const isVip = userProfile.profile.is_vip && 
    (!userProfile.profile.vip_expires_at || 
     new Date(userProfile.profile.vip_expires_at) > new Date())
  
  if (!isVip) {
    redirect('/vip')
  }
  
  return userProfile
}

// 客户端认证助手
export function useAuthClient() {
  const supabase = createBrowserClient()
  
  const signUp = async (email: string, password: string, metadata?: any) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata
      }
    })
    
    return { data, error }
  }
  
  const signIn = async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    
    return { data, error }
  }
  
  const signInWithProvider = async (provider: 'google' | 'github' | 'discord') => {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: `${window.location.origin}/auth/callback`
      }
    })
    
    return { data, error }
  }
  
  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    return { error }
  }
  
  const resetPassword = async (email: string) => {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`
    })
    
    return { data, error }
  }
  
  const updatePassword = async (password: string) => {
    const { data, error } = await supabase.auth.updateUser({
      password
    })
    
    return { data, error }
  }
  
  const updateProfile = async (updates: any) => {
    const { data, error } = await supabase.auth.updateUser({
      data: updates
    })
    
    return { data, error }
  }
  
  return {
    signUp,
    signIn,
    signInWithProvider,
    signOut,
    resetPassword,
    updatePassword,
    updateProfile
  }
}

// 权限检查助手
export async function checkPermissions(userId: string) {
  const supabase = await createClient()
  
  const { data: profile } = await supabase
    .from('users')
    .select('is_verified, is_vip, vip_expires_at')
    .eq('id', userId)
    .single()
  
  if (!profile) {
    return {
      isAuthenticated: false,
      isVerified: false,
      isVip: false,
      canCreateAdvancedActivity: false
    }
  }
  
  const isVip = profile.is_vip && 
    (!profile.vip_expires_at || new Date(profile.vip_expires_at) > new Date())
  
  return {
    isAuthenticated: true,
    isVerified: profile.is_verified,
    isVip,
    canCreateAdvancedActivity: isVip
  }
}

// 会话管理
export async function refreshSession() {
  const supabase = await createClient()
  
  const { data, error } = await supabase.auth.refreshSession()
  return { data, error }
}

export async function getSession() {
  const supabase = await createClient()
  
  const { data: { session }, error } = await supabase.auth.getSession()
  return { session, error }
}
