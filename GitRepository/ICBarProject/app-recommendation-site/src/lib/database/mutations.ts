import { createClient } from '@/lib/supabase/server'
import { Database } from '@/types/database'

type Tables = Database['public']['Tables']
type AppInsert = Tables['apps']['Insert']
type AppUpdate = Tables['apps']['Update']
type ActivityInsert = Tables['activities']['Insert']
type ActivityUpdate = Tables['activities']['Update']
type UserUpdate = Tables['users']['Update']

// 应用相关操作
export async function createApp(appData: Omit<AppInsert, 'id' | 'created_at' | 'updated_at'>) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('apps')
    .insert(appData)
    .select()
    .single()

  if (error) throw error
  return data
}

export async function updateApp(id: string, updates: AppUpdate) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('apps')
    .update(updates)
    .eq('id', id)
    .select()
    .single()

  if (error) throw error
  return data
}

export async function deleteApp(id: string) {
  const supabase = await createClient()
  
  const { error } = await supabase
    .from('apps')
    .delete()
    .eq('id', id)

  if (error) throw error
}

// 活动相关操作
export async function createActivity(activityData: Omit<ActivityInsert, 'id' | 'created_at' | 'updated_at'>) {
  const supabase = await createClient()
  
  // 验证邀请码数量
  if (activityData.invite_codes.length !== activityData.total_codes) {
    throw new Error('邀请码数量与总数不匹配')
  }

  const { data, error } = await supabase
    .from('activities')
    .insert(activityData)
    .select()
    .single()

  if (error) throw error
  return data
}

export async function updateActivity(id: string, updates: ActivityUpdate) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('activities')
    .update(updates)
    .eq('id', id)
    .select()
    .single()

  if (error) throw error
  return data
}

export async function deactivateActivity(id: string) {
  return updateActivity(id, { is_active: false })
}

export async function deleteActivity(id: string) {
  const supabase = await createClient()
  
  const { error } = await supabase
    .from('activities')
    .delete()
    .eq('id', id)

  if (error) throw error
}

// 邀请码领取操作
export async function claimInviteCode(
  activityId: string, 
  userId: string, 
  inviteCode: string
) {
  const supabase = await createClient()
  
  // 开始事务
  const { data: activity, error: activityError } = await supabase
    .from('activities')
    .select('invite_codes, used_codes, total_codes, is_active, expires_at')
    .eq('id', activityId)
    .single()

  if (activityError) throw activityError
  
  // 验证活动状态
  if (!activity.is_active) {
    throw new Error('活动已结束')
  }
  
  if (activity.expires_at && new Date(activity.expires_at) < new Date()) {
    throw new Error('活动已过期')
  }
  
  if (activity.used_codes >= activity.total_codes) {
    throw new Error('邀请码已全部领取完毕')
  }
  
  if (!activity.invite_codes.includes(inviteCode)) {
    throw new Error('无效的邀请码')
  }

  // 检查用户是否已经领取过
  const { data: existingClaim } = await supabase
    .from('code_claims')
    .select('id')
    .eq('activity_id', activityId)
    .eq('user_id', userId)
    .single()

  if (existingClaim) {
    throw new Error('您已经领取过此活动的邀请码')
  }

  // 创建领取记录
  const { data: claim, error: claimError } = await supabase
    .from('code_claims')
    .insert({
      activity_id: activityId,
      user_id: userId,
      invite_code: inviteCode
    })
    .select()
    .single()

  if (claimError) throw claimError

  // 更新活动的已使用数量
  const { error: updateError } = await supabase
    .from('activities')
    .update({ used_codes: activity.used_codes + 1 })
    .eq('id', activityId)

  if (updateError) throw updateError

  return claim
}

export async function submitFeedback(
  claimId: string,
  feedbackData: Record<string, any>
) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('code_claims')
    .update({
      feedback_submitted: true,
      feedback_data: feedbackData
    })
    .eq('id', claimId)
    .select()
    .single()

  if (error) throw error
  return data
}

// 用户订阅操作
export async function subscribeToApp(userId: string, appId: string) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('user_subscriptions')
    .insert({
      user_id: userId,
      app_id: appId
    })
    .select()
    .single()

  if (error) {
    if (error.code === '23505') { // 唯一约束违反
      throw new Error('您已经订阅了此应用')
    }
    throw error
  }
  
  return data
}

export async function unsubscribeFromApp(userId: string, appId: string) {
  const supabase = await createClient()
  
  const { error } = await supabase
    .from('user_subscriptions')
    .delete()
    .eq('user_id', userId)
    .eq('app_id', appId)

  if (error) throw error
}

// 用户资料操作
export async function updateUserProfile(userId: string, updates: UserUpdate) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('users')
    .update(updates)
    .eq('id', userId)
    .select()
    .single()

  if (error) throw error
  return data
}

export async function verifyUser(userId: string) {
  return updateUserProfile(userId, { is_verified: true })
}

export async function upgradeToVip(userId: string, expiresAt?: string) {
  return updateUserProfile(userId, { 
    is_vip: true,
    vip_expires_at: expiresAt 
  })
}

// 批量操作
export async function batchCreateActivities(activities: Omit<ActivityInsert, 'id' | 'created_at' | 'updated_at'>[]) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('activities')
    .insert(activities)
    .select()

  if (error) throw error
  return data
}

// 管理员操作
export async function getActivityStats(activityId: string) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('code_claims')
    .select(`
      id,
      claimed_at,
      feedback_submitted,
      users (
        username,
        is_verified
      )
    `)
    .eq('activity_id', activityId)
    .order('claimed_at', { ascending: false })

  if (error) throw error
  return data
}

export async function updateAppHotScore(appId: string) {
  const supabase = await createClient()
  
  const { error } = await supabase
    .rpc('update_app_hot_score', { app_uuid: appId })

  if (error) throw error
}
