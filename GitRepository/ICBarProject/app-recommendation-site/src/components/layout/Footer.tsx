import Link from 'next/link'

export default function Footer() {
  const currentYear = new Date().getFullYear()
  
  const footerLinks = {
    product: [
      { name: '应用推荐', href: '/apps' },
      { name: '送码活动', href: '/activities' },
      { name: 'VIP会员', href: '/vip' },
      { name: '实名认证', href: '/verification' },
    ],
    company: [
      { name: '关于我们', href: '/about' },
      { name: '联系我们', href: '/contact' },
      { name: '用户协议', href: '/terms' },
      { name: '隐私政策', href: '/privacy' },
    ],
    support: [
      { name: '帮助中心', href: '/help' },
      { name: '开发者指南', href: '/docs' },
      { name: '反馈建议', href: '/feedback' },
      { name: 'API文档', href: '/api-docs' },
    ],
    social: [
      { name: '微信公众号', href: '#' },
      { name: '新浪微博', href: '#' },
      { name: 'QQ群', href: '#' },
      { name: '邮件订阅', href: '#' },
    ]
  }
  
  return (
    <footer className="bg-white border-t border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Logo和描述 */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">码</span>
              </div>
              <span className="text-xl font-bold text-gray-900">送码网</span>
            </div>
            <p className="text-gray-600 text-sm leading-relaxed mb-4">
              基于送码活动的应用发现平台，让优质应用被更多人发现，让用户获得更多优惠。
            </p>
            <div className="flex space-x-4">
              <div className="text-sm text-gray-500">
                <div className="font-medium">联系邮箱</div>
                <div><EMAIL></div>
              </div>
            </div>
          </div>
          
          {/* 产品链接 */}
          <div>
            <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
              产品功能
            </h3>
            <ul className="space-y-3">
              {footerLinks.product.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-600 hover:text-gray-900 text-sm transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          
          {/* 公司链接 */}
          <div>
            <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
              公司信息
            </h3>
            <ul className="space-y-3">
              {footerLinks.company.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-600 hover:text-gray-900 text-sm transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          
          {/* 支持链接 */}
          <div>
            <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
              支持帮助
            </h3>
            <ul className="space-y-3">
              {footerLinks.support.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-600 hover:text-gray-900 text-sm transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          
          {/* 社交媒体 */}
          <div>
            <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
              关注我们
            </h3>
            <ul className="space-y-3">
              {footerLinks.social.map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    className="text-gray-600 hover:text-gray-900 text-sm transition-colors"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
            
            {/* 统计信息 */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="text-sm text-gray-500">
                <div className="mb-2">
                  <span className="font-medium text-blue-600">1000+</span> 应用
                </div>
                <div className="mb-2">
                  <span className="font-medium text-green-600">5000+</span> 活动
                </div>
                <div>
                  <span className="font-medium text-purple-600">10000+</span> 用户
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* 底部版权信息 */}
        <div className="mt-8 pt-8 border-t border-gray-200">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-sm text-gray-500">
              © {currentYear} 送码网. 保留所有权利.
            </div>
            
            <div className="flex items-center space-x-6 mt-4 md:mt-0">
              <div className="text-sm text-gray-500">
                <span>ICP备案号: 京ICP备12345678号</span>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="text-sm text-gray-500">
                  <span>安全认证</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 bg-green-100 rounded flex items-center justify-center">
                    <span className="text-green-600 text-xs">✓</span>
                  </div>
                  <span className="text-xs text-gray-500">SSL加密</span>
                </div>
              </div>
            </div>
          </div>
          
          {/* 友情链接 */}
          <div className="mt-6 pt-6 border-t border-gray-200">
            <div className="text-sm text-gray-500 mb-3">友情链接:</div>
            <div className="flex flex-wrap gap-4">
              <a href="#" className="text-sm text-gray-600 hover:text-gray-900">应用商店</a>
              <a href="#" className="text-sm text-gray-600 hover:text-gray-900">开发者社区</a>
              <a href="#" className="text-sm text-gray-600 hover:text-gray-900">技术博客</a>
              <a href="#" className="text-sm text-gray-600 hover:text-gray-900">产品论坛</a>
              <a href="#" className="text-sm text-gray-600 hover:text-gray-900">用户反馈</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
