'use client'

import { useState, useEffect } from 'react'
import { useSearchParams, useRouter, usePathname } from 'next/navigation'
import AppCard from './AppCard'
import { Database } from '@/types/database'
import { MagnifyingGlassIcon, FunnelIcon } from '@heroicons/react/24/outline'

type App = Database['public']['Tables']['apps']['Row'] & {
  users: {
    username: string | null
    full_name: string | null
    is_verified: boolean
    is_vip: boolean
  } | null
}

interface Category {
  id: string
  name: string
  icon: string
  count: number
}

interface AppListProps {
  initialApps?: App[]
  initialCategories?: Category[]
  showSearch?: boolean
  showFilters?: boolean
  className?: string
}

export default function AppList({ 
  initialApps = [], 
  initialCategories = [],
  showSearch = true,
  showFilters = true,
  className = ''
}: AppListProps) {
  const [apps, setApps] = useState<App[]>(initialApps)
  const [categories, setCategories] = useState<Category[]>(initialCategories)
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [sortBy, setSortBy] = useState('hot_score')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  
  const searchParams = useSearchParams()
  const router = useRouter()
  const pathname = usePathname()

  // 从URL参数初始化状态
  useEffect(() => {
    const query = searchParams.get('query') || ''
    const category = searchParams.get('category') || 'all'
    const sort = searchParams.get('sort') || 'hot_score'
    const page = parseInt(searchParams.get('page') || '1')
    
    setSearchQuery(query)
    setSelectedCategory(category)
    setSortBy(sort)
    setCurrentPage(page)
  }, [searchParams])

  // 获取应用列表
  const fetchApps = async (params: {
    query?: string
    category?: string
    sort_by?: string
    page?: number
  } = {}) => {
    setLoading(true)
    
    try {
      const queryParams = new URLSearchParams()
      
      if (params.query) queryParams.set('query', params.query)
      if (params.category && params.category !== 'all') queryParams.set('category', params.category)
      if (params.sort_by) queryParams.set('sort_by', params.sort_by)
      if (params.page) queryParams.set('page', params.page.toString())
      
      const response = await fetch(`/api/apps?${queryParams.toString()}`)
      
      if (!response.ok) {
        throw new Error('获取应用列表失败')
      }
      
      const data = await response.json()
      setApps(data.apps)
      setTotalPages(data.pagination.pages)
      
    } catch (error) {
      console.error('获取应用列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/apps/categories')
      
      if (!response.ok) {
        throw new Error('获取分类列表失败')
      }
      
      const data = await response.json()
      setCategories(data.categories)
      
    } catch (error) {
      console.error('获取分类列表失败:', error)
    }
  }

  // 更新URL参数
  const updateURL = (params: Record<string, string | number>) => {
    const newSearchParams = new URLSearchParams(searchParams.toString())
    
    Object.entries(params).forEach(([key, value]) => {
      if (value && value !== 'all' && value !== 1) {
        newSearchParams.set(key, value.toString())
      } else {
        newSearchParams.delete(key)
      }
    })
    
    const newURL = `${pathname}?${newSearchParams.toString()}`
    router.push(newURL, { scroll: false })
  }

  // 处理搜索
  const handleSearch = (query: string) => {
    setSearchQuery(query)
    setCurrentPage(1)
    updateURL({ query, category: selectedCategory, sort: sortBy, page: 1 })
    fetchApps({ query, category: selectedCategory, sort_by: sortBy, page: 1 })
  }

  // 处理分类筛选
  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category)
    setCurrentPage(1)
    updateURL({ query: searchQuery, category, sort: sortBy, page: 1 })
    fetchApps({ query: searchQuery, category, sort_by: sortBy, page: 1 })
  }

  // 处理排序
  const handleSortChange = (sort: string) => {
    setSortBy(sort)
    setCurrentPage(1)
    updateURL({ query: searchQuery, category: selectedCategory, sort, page: 1 })
    fetchApps({ query: searchQuery, category: selectedCategory, sort_by: sort, page: 1 })
  }

  // 处理分页
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    updateURL({ query: searchQuery, category: selectedCategory, sort: sortBy, page })
    fetchApps({ query: searchQuery, category: selectedCategory, sort_by: sortBy, page })
  }

  // 初始化数据
  useEffect(() => {
    if (categories.length === 0) {
      fetchCategories()
    }
    
    if (apps.length === 0) {
      fetchApps({
        query: searchQuery,
        category: selectedCategory,
        sort_by: sortBy,
        page: currentPage
      })
    }
  }, [])

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 搜索和筛选 */}
      {(showSearch || showFilters) && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          {/* 搜索框 */}
          {showSearch && (
            <div className="mb-4">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="搜索应用名称、描述或开发者..."
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      handleSearch(searchQuery)
                    }
                  }}
                />
              </div>
            </div>
          )}
          
          {/* 筛选器 */}
          {showFilters && (
            <div className="flex flex-wrap items-center gap-4">
              {/* 分类筛选 */}
              <div className="flex items-center space-x-2">
                <FunnelIcon className="h-5 w-5 text-gray-400" />
                <select
                  value={selectedCategory}
                  onChange={(e) => handleCategoryChange(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                >
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.icon} {category.name} ({category.count})
                    </option>
                  ))}
                </select>
              </div>
              
              {/* 排序 */}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500">排序:</span>
                <select
                  value={sortBy}
                  onChange={(e) => handleSortChange(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="hot_score">热度排序</option>
                  <option value="activity_count">活动数量</option>
                  <option value="created_at">最新发布</option>
                </select>
              </div>
              
              {/* 搜索按钮 */}
              {showSearch && (
                <button
                  onClick={() => handleSearch(searchQuery)}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  搜索
                </button>
              )}
            </div>
          )}
        </div>
      )}
      
      {/* 应用列表 */}
      {loading ? (
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : apps.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {apps.map((app) => (
            <AppCard key={app.id} app={app} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-500 text-lg mb-2">暂无应用</div>
          <div className="text-gray-400 text-sm">
            {searchQuery || selectedCategory !== 'all' 
              ? '尝试调整搜索条件或筛选器' 
              : '还没有应用发布，成为第一个发布者吧！'
            }
          </div>
        </div>
      )}
      
      {/* 分页 */}
      {totalPages > 1 && (
        <div className="flex justify-center">
          <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>
            
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                  page === currentPage
                    ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                }`}
              >
                {page}
              </button>
            ))}
            
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>
          </nav>
        </div>
      )}
    </div>
  )
}
