'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { 
  ChatBubbleLeftRightIcon, 
  CheckCircleIcon,
  ExclamationTriangleIcon 
} from '@heroicons/react/24/outline'

const feedbackFormSchema = z.object({
  answers: z.record(z.string().min(1, '请填写此问题的答案'))
})

type FeedbackFormData = z.infer<typeof feedbackFormSchema>

interface FeedbackFormProps {
  claimId: string
  questions: string[]
  onSuccess?: () => void
  className?: string
}

export default function FeedbackForm({ 
  claimId, 
  questions, 
  onSuccess, 
  className = '' 
}: FeedbackFormProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<FeedbackFormData>({
    resolver: zod<PERSON><PERSON><PERSON><PERSON>(feedbackFormSchema)
  })

  const onSubmit = async (data: FeedbackFormData) => {
    setLoading(true)
    setError('')

    try {
      const response = await fetch(`/api/activities/feedback`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          claim_id: claimId,
          feedback_data: data.answers
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || '提交反馈失败')
      }

      setSuccess(true)
      reset()
      
      if (onSuccess) {
        onSuccess()
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : '提交反馈失败')
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className={`bg-green-50 border border-green-200 rounded-lg p-6 ${className}`}>
        <div className="flex items-center">
          <CheckCircleIcon className="w-6 h-6 text-green-600 mr-3" />
          <div>
            <h3 className="text-lg font-medium text-green-800">反馈提交成功</h3>
            <p className="text-green-700 mt-1">
              感谢您的反馈，这将帮助我们改进应用体验！
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-6 ${className}`}>
      <div className="flex items-center mb-6">
        <ChatBubbleLeftRightIcon className="w-6 h-6 text-blue-600 mr-3" />
        <div>
          <h3 className="text-lg font-medium text-gray-900">提交使用反馈</h3>
          <p className="text-gray-600 mt-1">
            请分享您的使用体验，帮助开发者改进应用
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {error && (
          <div className="rounded-md bg-red-50 p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="w-5 h-5 text-red-400 mr-3 mt-0.5" />
              <div className="text-sm text-red-700">{error}</div>
            </div>
          </div>
        )}

        {questions.map((question, index) => (
          <div key={index}>
            <label 
              htmlFor={`question-${index}`} 
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              {index + 1}. {question}
            </label>
            <textarea
              id={`question-${index}`}
              rows={3}
              {...register(`answers.${index}`)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="请详细描述您的体验和建议..."
            />
            {errors.answers?.[index] && (
              <p className="mt-1 text-sm text-red-600">
                {errors.answers[index]?.message}
              </p>
            )}
          </div>
        ))}

        <div className="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200">
          <button
            type="submit"
            disabled={loading}
            className="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                提交中...
              </>
            ) : (
              <>
                <ChatBubbleLeftRightIcon className="w-4 h-4 mr-2" />
                提交反馈
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  )
}
