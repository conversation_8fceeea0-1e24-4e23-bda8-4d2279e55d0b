'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useAuth } from '@/components/auth/AuthProvider'
import { GiftIcon, CheckCircleIcon } from '@heroicons/react/24/outline'

const claimFormSchema = z.object({
  invite_code: z.string().min(1, '请输入邀请码')
})

type ClaimFormData = z.infer<typeof claimFormSchema>

interface ClaimCodeFormProps {
  activityId: string
  onSuccess?: (claim: any) => void
  className?: string
}

export default function ClaimCodeForm({ activityId, onSuccess, className = '' }: ClaimCodeFormProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState<any>(null)
  const { user } = useAuth()
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<ClaimFormData>({
    resolver: zodResolver(claimFormSchema)
  })
  
  const onSubmit = async (data: ClaimFormData) => {
    if (!user) {
      setError('请先登录')
      return
    }
    
    setLoading(true)
    setError('')
    
    try {
      const response = await fetch(`/api/activities/${activityId}/claim`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      })
      
      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.error || '领取失败')
      }
      
      setSuccess(result.claim)
      reset()
      
      if (onSuccess) {
        onSuccess(result.claim)
      }
      
    } catch (err) {
      setError(err instanceof Error ? err.message : '领取失败')
    } finally {
      setLoading(false)
    }
  }
  
  if (success) {
    return (
      <div className={`bg-green-50 border border-green-200 rounded-lg p-6 ${className}`}>
        <div className="flex items-center mb-4">
          <CheckCircleIcon className="w-6 h-6 text-green-600 mr-2" />
          <h3 className="text-lg font-medium text-green-900">邀请码领取成功！</h3>
        </div>
        
        <div className="space-y-3">
          <div className="bg-white rounded-lg p-4 border border-green-200">
            <div className="text-sm text-gray-600 mb-1">您的邀请码</div>
            <div className="text-2xl font-mono font-bold text-green-700 tracking-wider">
              {success.invite_code}
            </div>
          </div>
          
          <div className="text-sm text-gray-600">
            <p className="mb-2">
              <strong>应用:</strong> {success.app_name}
            </p>
            <p className="mb-2">
              <strong>活动:</strong> {success.activity_title}
            </p>
            <p className="mb-2">
              <strong>领取时间:</strong> {new Date(success.claimed_at).toLocaleString()}
            </p>
          </div>
          
          {success.feedback_required && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="text-sm text-blue-800">
                <strong>注意:</strong> 此活动需要您在使用邀请码后提交反馈。
                请在使用完邀请码后回到活动页面提交反馈。
              </div>
            </div>
          )}
          
          <div className="flex space-x-3">
            <button
              onClick={() => setSuccess(null)}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              继续领取
            </button>
            
            <button
              onClick={() => {
                navigator.clipboard.writeText(success.invite_code)
                // 可以添加复制成功的提示
              }}
              className="flex-1 px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              复制邀请码
            </button>
          </div>
        </div>
      </div>
    )
  }
  
  if (!user) {
    return (
      <div className={`bg-yellow-50 border border-yellow-200 rounded-lg p-6 ${className}`}>
        <div className="text-center">
          <GiftIcon className="w-12 h-12 text-yellow-600 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-yellow-900 mb-2">需要登录</h3>
          <p className="text-yellow-700 mb-4">请先登录后再领取邀请码</p>
          <a
            href="/login"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            前往登录
          </a>
        </div>
      </div>
    )
  }
  
  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-6 ${className}`}>
      <div className="flex items-center mb-4">
        <GiftIcon className="w-6 h-6 text-blue-600 mr-2" />
        <h3 className="text-lg font-medium text-gray-900">领取邀请码</h3>
      </div>
      
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        {error && (
          <div className="rounded-md bg-red-50 p-4">
            <div className="text-sm text-red-700">{error}</div>
          </div>
        )}
        
        <div>
          <label htmlFor="invite_code" className="block text-sm font-medium text-gray-700 mb-2">
            邀请码
          </label>
          <input
            type="text"
            id="invite_code"
            {...register('invite_code')}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 font-mono tracking-wider"
            placeholder="请输入邀请码"
            autoComplete="off"
          />
          {errors.invite_code && (
            <p className="mt-1 text-sm text-red-600">{errors.invite_code.message}</p>
          )}
          <p className="mt-1 text-sm text-gray-500">
            请输入活动提供的邀请码，区分大小写
          </p>
        </div>
        
        <button
          type="submit"
          disabled={loading}
          className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              领取中...
            </div>
          ) : (
            '领取邀请码'
          )}
        </button>
      </form>
      
      <div className="mt-4 text-xs text-gray-500">
        <p>• 每个活动每个用户只能领取一次邀请码</p>
        <p>• 请确保满足活动的所有参与条件</p>
        <p>• 邀请码领取后请及时使用，避免过期</p>
      </div>
    </div>
  )
}
