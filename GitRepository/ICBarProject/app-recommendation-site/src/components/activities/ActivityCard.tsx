'use client'

import Link from 'next/link'
import Image from 'next/image'
import { Database } from '@/types/database'
import { formatTimeAgo, formatExpiryTime, isActivityActive, getActivityStatus } from '@/lib/utils/business'
import { ClockIcon, UserIcon, GiftIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline'
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid'

type Activity = Database['public']['Tables']['activities']['Row'] & {
  apps: {
    id: string
    name: string
    icon_url: string | null
    category: string
    developer: string
  } | null
  users: {
    username: string | null
    full_name: string | null
    is_verified: boolean
    is_vip: boolean
  } | null
}

interface ActivityCardProps {
  activity: Activity
  showAppInfo?: boolean
  className?: string
}

export default function ActivityCard({ activity, showAppInfo = true, className = '' }: ActivityCardProps) {
  const activityStatus = getActivityStatus(activity)
  const remainingCodes = activity.total_codes - activity.used_codes
  const claimRate = activity.total_codes > 0 ? (activity.used_codes / activity.total_codes) * 100 : 0
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-100'
      case 'expired':
        return 'text-red-600 bg-red-100'
      case 'full':
        return 'text-yellow-600 bg-yellow-100'
      case 'inactive':
        return 'text-gray-600 bg-gray-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }
  
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200 ${className}`}>
      <div className="p-6">
        {/* 活动头部 */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <Link
                href={`/activities/${activity.id}`}
                className="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors"
              >
                {activity.title}
              </Link>
              
              {/* VIP标识 */}
              {activity.users?.is_vip && (
                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                  <StarIconSolid className="w-3 h-3 mr-1" />
                  VIP
                </span>
              )}
            </div>
            
            <p className="text-gray-600 text-sm line-clamp-2 mb-3">
              {activity.description}
            </p>
          </div>
          
          {/* 状态标识 */}
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(activityStatus.status)}`}>
            {activityStatus.status === 'active' && <CheckCircleIcon className="w-3 h-3 mr-1" />}
            {activityStatus.status === 'expired' && <XCircleIcon className="w-3 h-3 mr-1" />}
            {activityStatus.status === 'full' && <GiftIcon className="w-3 h-3 mr-1" />}
            {activityStatus.message}
          </span>
        </div>
        
        {/* 应用信息 */}
        {showAppInfo && activity.apps && (
          <div className="flex items-center space-x-3 mb-4 p-3 bg-gray-50 rounded-lg">
            {activity.apps.icon_url ? (
              <Image
                src={activity.apps.icon_url}
                alt={activity.apps.name}
                width={40}
                height={40}
                className="rounded-lg"
              />
            ) : (
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white text-sm font-bold">
                {activity.apps.name.charAt(0).toUpperCase()}
              </div>
            )}
            
            <div className="flex-1 min-w-0">
              <Link
                href={`/apps/${activity.apps.id}`}
                className="text-sm font-medium text-gray-900 hover:text-blue-600 transition-colors truncate block"
              >
                {activity.apps.name}
              </Link>
              <p className="text-xs text-gray-500">{activity.apps.developer}</p>
            </div>
          </div>
        )}
        
        {/* 活动统计 */}
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900">{activity.total_codes}</div>
            <div className="text-xs text-gray-500">总邀请码</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-blue-600">{remainingCodes}</div>
            <div className="text-xs text-gray-500">剩余</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-green-600">{claimRate.toFixed(0)}%</div>
            <div className="text-xs text-gray-500">领取率</div>
          </div>
        </div>
        
        {/* 进度条 */}
        <div className="mb-4">
          <div className="flex justify-between text-xs text-gray-500 mb-1">
            <span>领取进度</span>
            <span>{activity.used_codes}/{activity.total_codes}</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${claimRate}%` }}
            />
          </div>
        </div>
        
        {/* 活动条件 */}
        {(activity.requires_verification || activity.requires_subscription || activity.feedback_required) && (
          <div className="flex flex-wrap gap-2 mb-4">
            {activity.requires_verification && (
              <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                需要实名认证
              </span>
            )}
            {activity.requires_subscription && (
              <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-purple-100 text-purple-800">
                需要订阅应用
              </span>
            )}
            {activity.feedback_required && (
              <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
                需要反馈
              </span>
            )}
          </div>
        )}
        
        {/* 活动信息 */}
        <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
          <div className="flex items-center space-x-4">
            <span className="flex items-center">
              <UserIcon className="w-4 h-4 mr-1" />
              {activity.users?.full_name || activity.users?.username || '匿名用户'}
            </span>
            
            <span className="flex items-center">
              <ClockIcon className="w-4 h-4 mr-1" />
              {formatTimeAgo(activity.created_at)}
            </span>
          </div>
          
          {activity.expires_at && (
            <div className="text-right">
              <div className="text-xs text-gray-400">截止时间</div>
              <div className={`text-xs ${new Date(activity.expires_at) <= new Date() ? 'text-red-600' : 'text-gray-600'}`}>
                {formatExpiryTime(activity.expires_at)}
              </div>
            </div>
          )}
        </div>
        
        {/* 操作按钮 */}
        <div className="flex items-center justify-between">
          <Link
            href={`/activities/${activity.id}`}
            className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            查看详情
          </Link>
          
          {activityStatus.status === 'active' ? (
            <Link
              href={`/activities/${activity.id}`}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <GiftIcon className="w-4 h-4 mr-2" />
              领取邀请码
            </Link>
          ) : (
            <span className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-500 bg-gray-100 cursor-not-allowed">
              {activityStatus.message}
            </span>
          )}
        </div>
      </div>
    </div>
  )
}
