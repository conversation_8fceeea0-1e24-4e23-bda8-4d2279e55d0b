import { render, screen, fireEvent } from '@testing-library/react'
import { AuthProvider } from '@/components/auth/AuthProvider'
import Navbar from '@/components/layout/Navbar'

// Mock the auth context
const mockAuthContext = {
  user: null,
  profile: null,
  loading: false,
  signOut: jest.fn(),
  refreshProfile: jest.fn()
}

jest.mock('@/components/auth/AuthProvider', () => ({
  ...jest.requireActual('@/components/auth/AuthProvider'),
  useAuth: () => mockAuthContext,
  usePermissions: () => ({
    isAuthenticated: false,
    isVerified: false,
    isVip: false,
    canCreateAdvancedActivity: false
  })
}))

const renderNavbar = () => {
  return render(
    <AuthProvider>
      <Navbar />
    </AuthProvider>
  )
}

describe('Navbar', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the logo and navigation links', () => {
    renderNavbar()
    
    // Check logo
    expect(screen.getByText('送码网')).toBeInTheDocument()
    
    // Check navigation links
    expect(screen.getByText('首页')).toBeInTheDocument()
    expect(screen.getByText('应用')).toBeInTheDocument()
    expect(screen.getByText('活动')).toBeInTheDocument()
  })

  it('shows login button when user is not authenticated', () => {
    renderNavbar()
    
    expect(screen.getByText('登录')).toBeInTheDocument()
    expect(screen.getByText('注册')).toBeInTheDocument()
  })

  it('shows user menu when user is authenticated', () => {
    // Mock authenticated user
    mockAuthContext.user = {
      id: '1',
      email: '<EMAIL>',
      created_at: '2023-01-01'
    }
    mockAuthContext.profile = {
      username: 'testuser',
      full_name: 'Test User',
      is_verified: false,
      is_vip: false
    }

    renderNavbar()
    
    expect(screen.getByText('testuser')).toBeInTheDocument()
  })

  it('opens mobile menu when hamburger button is clicked', () => {
    renderNavbar()
    
    const mobileMenuButton = screen.getByRole('button', { name: /打开主菜单/i })
    fireEvent.click(mobileMenuButton)
    
    // Mobile menu should be visible
    expect(screen.getAllByText('首页')).toHaveLength(2) // Desktop + Mobile
  })

  it('calls signOut when logout is clicked', () => {
    // Mock authenticated user
    mockAuthContext.user = {
      id: '1',
      email: '<EMAIL>',
      created_at: '2023-01-01'
    }
    mockAuthContext.profile = {
      username: 'testuser',
      full_name: 'Test User',
      is_verified: false,
      is_vip: false
    }

    renderNavbar()
    
    // Open user menu
    const userMenuButton = screen.getByText('testuser')
    fireEvent.click(userMenuButton)
    
    // Click logout
    const logoutButton = screen.getByText('退出登录')
    fireEvent.click(logoutButton)
    
    expect(mockAuthContext.signOut).toHaveBeenCalled()
  })

  it('displays VIP badge for VIP users', () => {
    // Mock VIP user
    mockAuthContext.user = {
      id: '1',
      email: '<EMAIL>',
      created_at: '2023-01-01'
    }
    mockAuthContext.profile = {
      username: 'vipuser',
      full_name: 'VIP User',
      is_verified: true,
      is_vip: true,
      vip_expires_at: '2024-12-31'
    }

    // Mock VIP permissions
    jest.mocked(require('@/components/auth/AuthProvider').usePermissions).mockReturnValue({
      isAuthenticated: true,
      isVerified: true,
      isVip: true,
      canCreateAdvancedActivity: true
    })

    renderNavbar()
    
    expect(screen.getByText('VIP')).toBeInTheDocument()
  })
})
