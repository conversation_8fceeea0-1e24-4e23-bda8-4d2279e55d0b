'use client'

import { useState, useEffect } from 'react'
import { RequireAuth, useAuth, usePermissions } from '@/components/auth/AuthProvider'
import { 
  UserIcon, 
  EnvelopeIcon, 
  CalendarIcon,
  CheckBadgeIcon,
  StarIcon,
  GiftIcon,
  RectangleStackIcon,
  ChartBarIcon,
  CogIcon
} from '@heroicons/react/24/outline'
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid'
import Link from 'next/link'

interface UserStats {
  totalApps: number
  totalActivities: number
  totalClaims: number
  totalFeedbacks: number
}

export default function ProfilePage() {
  const { user, profile } = useAuth()
  const { isVip, isVerified } = usePermissions()
  const [stats, setStats] = useState<UserStats>({
    totalApps: 0,
    totalActivities: 0,
    totalClaims: 0,
    totalFeedbacks: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchUserStats = async () => {
      try {
        const response = await fetch('/api/profile/stats')
        if (response.ok) {
          const data = await response.json()
          setStats(data.stats)
        }
      } catch (error) {
        console.error('获取用户统计失败:', error)
      } finally {
        setLoading(false)
      }
    }

    if (user) {
      fetchUserStats()
    }
  }, [user])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const quickActions = [
    {
      name: '发布应用',
      href: '/apps/create',
      icon: RectangleStackIcon,
      description: '发布新的应用到平台',
      color: 'bg-blue-500 hover:bg-blue-600'
    },
    {
      name: '我的应用',
      href: '/profile/apps',
      icon: RectangleStackIcon,
      description: '管理已发布的应用',
      color: 'bg-green-500 hover:bg-green-600'
    },
    {
      name: '我的活动',
      href: '/profile/activities',
      icon: GiftIcon,
      description: '查看参与和创建的活动',
      color: 'bg-purple-500 hover:bg-purple-600'
    },
    {
      name: '账户设置',
      href: '/profile/settings',
      icon: CogIcon,
      description: '修改个人信息和偏好',
      color: 'bg-gray-500 hover:bg-gray-600'
    }
  ]

  return (
    <RequireAuth>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* 页面头部 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">个人中心</h1>
            <p className="mt-2 text-gray-600">
              管理您的账户信息和应用活动
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* 左侧：用户信息 */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                {/* 头像和基本信息 */}
                <div className="text-center mb-6">
                  <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <UserIcon className="w-10 h-10 text-white" />
                  </div>
                  
                  <div className="flex items-center justify-center space-x-2 mb-2">
                    <h2 className="text-xl font-semibold text-gray-900">
                      {profile?.username || '用户'}
                    </h2>
                    {isVerified && (
                      <CheckBadgeIcon className="w-5 h-5 text-blue-500" title="已实名认证" />
                    )}
                    {isVip && (
                      <div className="flex items-center">
                        <StarIconSolid className="w-5 h-5 text-yellow-500" />
                        <span className="text-xs font-medium text-yellow-600 ml-1">VIP</span>
                      </div>
                    )}
                  </div>
                  
                  {profile?.full_name && (
                    <p className="text-gray-600 mb-2">{profile.full_name}</p>
                  )}
                </div>

                {/* 用户详细信息 */}
                <div className="space-y-4">
                  <div className="flex items-center text-sm text-gray-600">
                    <EnvelopeIcon className="w-4 h-4 mr-3 flex-shrink-0" />
                    <span className="truncate">{user?.email}</span>
                  </div>
                  
                  <div className="flex items-center text-sm text-gray-600">
                    <CalendarIcon className="w-4 h-4 mr-3 flex-shrink-0" />
                    <span>加入时间：{user?.created_at ? formatDate(user.created_at) : '未知'}</span>
                  </div>

                  {/* VIP状态 */}
                  <div className="pt-4 border-t border-gray-200">
                    {isVip ? (
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <StarIconSolid className="w-5 h-5 text-yellow-500 mr-2" />
                          <span className="text-sm font-medium text-gray-900">VIP会员</span>
                        </div>
                        <span className="text-xs text-gray-500">
                          {profile?.vip_expires_at ? 
                            `到期：${formatDate(profile.vip_expires_at)}` : 
                            '永久有效'
                          }
                        </span>
                      </div>
                    ) : (
                      <div className="text-center">
                        <p className="text-sm text-gray-600 mb-3">升级VIP解锁更多功能</p>
                        <Link
                          href="/vip"
                          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700"
                        >
                          <StarIcon className="w-4 h-4 mr-2" />
                          升级VIP
                        </Link>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* 右侧：统计和快捷操作 */}
            <div className="lg:col-span-2 space-y-8">
              {/* 统计数据 */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-6">数据统计</h3>
                
                {loading ? (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {[...Array(4)].map((_, i) => (
                      <div key={i} className="animate-pulse">
                        <div className="h-16 bg-gray-200 rounded-lg"></div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{stats.totalApps}</div>
                      <div className="text-sm text-gray-600">发布应用</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{stats.totalActivities}</div>
                      <div className="text-sm text-gray-600">创建活动</div>
                    </div>
                    <div className="text-center p-4 bg-purple-50 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">{stats.totalClaims}</div>
                      <div className="text-sm text-gray-600">领取邀请码</div>
                    </div>
                    <div className="text-center p-4 bg-orange-50 rounded-lg">
                      <div className="text-2xl font-bold text-orange-600">{stats.totalFeedbacks}</div>
                      <div className="text-sm text-gray-600">提交反馈</div>
                    </div>
                  </div>
                )}
              </div>

              {/* 快捷操作 */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-6">快捷操作</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {quickActions.map((action) => {
                    const Icon = action.icon
                    return (
                      <Link
                        key={action.name}
                        href={action.href}
                        className="group relative bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                      >
                        <div className="flex items-start space-x-3">
                          <div className={`flex-shrink-0 w-10 h-10 ${action.color} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform`}>
                            <Icon className="w-5 h-5 text-white" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="text-sm font-medium text-gray-900 group-hover:text-blue-600">
                              {action.name}
                            </h4>
                            <p className="text-sm text-gray-500 mt-1">
                              {action.description}
                            </p>
                          </div>
                        </div>
                      </Link>
                    )
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </RequireAuth>
  )
}
