import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { claimCodeSchema } from '@/lib/validations/schemas'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: '未认证' },
        { status: 401 }
      )
    }
    
    const body = await request.json()
    
    // 验证输入数据
    const validation = claimCodeSchema.safeParse({
      activity_id: params.id,
      invite_code: body.invite_code
    })
    
    if (!validation.success) {
      return NextResponse.json(
        { error: '输入数据无效', details: validation.error.errors },
        { status: 400 }
      )
    }
    
    const { invite_code } = validation.data
    
    // 获取活动详情
    const { data: activity, error: activityError } = await supabase
      .from('activities')
      .select(`
        *,
        apps (
          id,
          name
        )
      `)
      .eq('id', params.id)
      .single()
    
    if (activityError) {
      if (activityError.code === 'PGRST116') {
        return NextResponse.json(
          { error: '活动不存在' },
          { status: 404 }
        )
      }
      
      return NextResponse.json(
        { error: '获取活动信息失败' },
        { status: 500 }
      )
    }
    
    // 检查活动状态
    if (!activity.is_active) {
      return NextResponse.json(
        { error: '活动已结束' },
        { status: 400 }
      )
    }
    
    if (activity.expires_at && new Date(activity.expires_at) < new Date()) {
      return NextResponse.json(
        { error: '活动已过期' },
        { status: 400 }
      )
    }
    
    if (activity.used_codes >= activity.total_codes) {
      return NextResponse.json(
        { error: '邀请码已全部领取完毕' },
        { status: 400 }
      )
    }
    
    // 验证邀请码
    if (!activity.invite_codes.includes(invite_code)) {
      return NextResponse.json(
        { error: '无效的邀请码' },
        { status: 400 }
      )
    }
    
    // 检查用户是否已经领取过
    const { data: existingClaim } = await supabase
      .from('code_claims')
      .select('id')
      .eq('activity_id', params.id)
      .eq('user_id', user.id)
      .single()
    
    if (existingClaim) {
      return NextResponse.json(
        { error: '您已经领取过此活动的邀请码' },
        { status: 409 }
      )
    }
    
    // 获取用户资料以检查条件
    const { data: userProfile, error: profileError } = await supabase
      .from('users')
      .select('is_verified')
      .eq('id', user.id)
      .single()
    
    if (profileError) {
      return NextResponse.json(
        { error: '获取用户资料失败' },
        { status: 500 }
      )
    }
    
    // 检查活动条件
    if (activity.requires_verification && !userProfile.is_verified) {
      return NextResponse.json(
        { error: '此活动需要完成实名认证' },
        { status: 403 }
      )
    }
    
    if (activity.requires_subscription && activity.required_apps) {
      // 检查用户是否订阅了必需的应用
      const { data: subscriptions } = await supabase
        .from('user_subscriptions')
        .select('app_id')
        .eq('user_id', user.id)
        .in('app_id', activity.required_apps)
      
      const subscribedApps = subscriptions?.map(sub => sub.app_id) || []
      const missingApps = activity.required_apps.filter(appId => !subscribedApps.includes(appId))
      
      if (missingApps.length > 0) {
        return NextResponse.json(
          { error: '您需要先订阅指定的应用才能领取邀请码' },
          { status: 403 }
        )
      }
    }
    
    // 开始事务：创建领取记录并更新活动统计
    const { data: claim, error: claimError } = await supabase
      .from('code_claims')
      .insert({
        activity_id: params.id,
        user_id: user.id,
        invite_code: invite_code
      })
      .select(`
        *,
        activities (
          title,
          feedback_required,
          feedback_questions,
          apps (
            name
          )
        )
      `)
      .single()
    
    if (claimError) {
      if (claimError.code === '23505') {
        return NextResponse.json(
          { error: '您已经领取过此活动的邀请码' },
          { status: 409 }
        )
      }
      
      console.error('创建领取记录失败:', claimError)
      return NextResponse.json(
        { error: '领取邀请码失败' },
        { status: 500 }
      )
    }
    
    // 更新活动的已使用数量
    const { error: updateError } = await supabase
      .from('activities')
      .update({ used_codes: activity.used_codes + 1 })
      .eq('id', params.id)
    
    if (updateError) {
      console.error('更新活动统计失败:', updateError)
      // 这里可以考虑回滚领取记录，但为了简化暂时不处理
    }
    
    return NextResponse.json({
      message: '邀请码领取成功',
      claim: {
        id: claim.id,
        invite_code: claim.invite_code,
        claimed_at: claim.claimed_at,
        activity_title: claim.activities?.title,
        app_name: claim.activities?.apps?.name,
        feedback_required: claim.activities?.feedback_required,
        feedback_questions: claim.activities?.feedback_questions
      }
    })
    
  } catch (error) {
    console.error('领取邀请码错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}

// 获取活动的领取统计（仅活动创建者可见）
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: '未认证' },
        { status: 401 }
      )
    }
    
    // 检查用户是否是活动创建者
    const { data: activity, error: activityError } = await supabase
      .from('activities')
      .select('created_by, title')
      .eq('id', params.id)
      .single()
    
    if (activityError) {
      return NextResponse.json(
        { error: '活动不存在' },
        { status: 404 }
      )
    }
    
    if (activity.created_by !== user.id) {
      return NextResponse.json(
        { error: '无权限查看此活动的领取统计' },
        { status: 403 }
      )
    }
    
    // 获取领取记录
    const { data: claims, error: claimsError } = await supabase
      .from('code_claims')
      .select(`
        id,
        invite_code,
        claimed_at,
        feedback_submitted,
        users (
          username,
          full_name,
          is_verified
        )
      `)
      .eq('activity_id', params.id)
      .order('claimed_at', { ascending: false })
    
    if (claimsError) {
      console.error('获取领取记录失败:', claimsError)
      return NextResponse.json(
        { error: '获取领取记录失败' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      activity_title: activity.title,
      claims: claims || [],
      stats: {
        total_claims: claims?.length || 0,
        feedback_submitted: claims?.filter(claim => claim.feedback_submitted).length || 0
      }
    })
    
  } catch (error) {
    console.error('获取领取统计错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
