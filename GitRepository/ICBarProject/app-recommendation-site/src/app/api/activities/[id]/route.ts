import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { activityUpdateSchema } from '@/lib/validations/schemas'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    
    const { data: activity, error } = await supabase
      .from('activities')
      .select(`
        *,
        apps (
          id,
          name,
          icon_url,
          category,
          developer,
          created_by
        ),
        users:created_by (
          username,
          full_name,
          is_verified,
          is_vip
        )
      `)
      .eq('id', params.id)
      .single()
    
    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: '活动不存在' },
          { status: 404 }
        )
      }
      
      console.error('获取活动详情失败:', error)
      return NextResponse.json(
        { error: '获取活动详情失败' },
        { status: 500 }
      )
    }
    
    // 计算活动统计
    const remainingCodes = activity.total_codes - activity.used_codes
    const claimRate = activity.total_codes > 0 ? (activity.used_codes / activity.total_codes) * 100 : 0
    
    // 检查活动状态
    const now = new Date()
    const isExpired = activity.expires_at && new Date(activity.expires_at) <= now
    const isFull = activity.used_codes >= activity.total_codes
    const isActive = activity.is_active && !isExpired && !isFull
    
    const activityWithStats = {
      ...activity,
      stats: {
        remaining_codes: remainingCodes,
        claim_rate: claimRate,
        is_expired: isExpired,
        is_full: isFull,
        is_claimable: isActive
      }
    }
    
    return NextResponse.json({ activity: activityWithStats })
    
  } catch (error) {
    console.error('获取活动详情错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: '未认证' },
        { status: 401 }
      )
    }
    
    // 检查活动是否存在且用户有权限修改
    const { data: existingActivity, error: fetchError } = await supabase
      .from('activities')
      .select('created_by, title')
      .eq('id', params.id)
      .single()
    
    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json(
          { error: '活动不存在' },
          { status: 404 }
        )
      }
      
      return NextResponse.json(
        { error: '获取活动信息失败' },
        { status: 500 }
      )
    }
    
    if (existingActivity.created_by !== user.id) {
      return NextResponse.json(
        { error: '无权限修改此活动' },
        { status: 403 }
      )
    }
    
    const body = await request.json()
    
    // 验证输入数据
    const validation = activityUpdateSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        { error: '输入数据无效', details: validation.error.errors },
        { status: 400 }
      )
    }
    
    const updates = validation.data
    
    // 如果更新邀请码，需要重新计算总数
    if (updates.invite_codes) {
      updates.total_codes = updates.invite_codes.length
    }
    
    // 更新活动
    const { data: activity, error: updateError } = await supabase
      .from('activities')
      .update(updates)
      .eq('id', params.id)
      .select(`
        *,
        apps (
          id,
          name,
          icon_url,
          category,
          developer
        ),
        users:created_by (
          username,
          full_name,
          is_verified,
          is_vip
        )
      `)
      .single()
    
    if (updateError) {
      console.error('更新活动失败:', updateError)
      return NextResponse.json(
        { error: '更新活动失败' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      message: '活动更新成功',
      activity
    })
    
  } catch (error) {
    console.error('更新活动错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: '未认证' },
        { status: 401 }
      )
    }
    
    // 检查活动是否存在且用户有权限删除
    const { data: existingActivity, error: fetchError } = await supabase
      .from('activities')
      .select('created_by, title, used_codes')
      .eq('id', params.id)
      .single()
    
    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json(
          { error: '活动不存在' },
          { status: 404 }
        )
      }
      
      return NextResponse.json(
        { error: '获取活动信息失败' },
        { status: 500 }
      )
    }
    
    if (existingActivity.created_by !== user.id) {
      return NextResponse.json(
        { error: '无权限删除此活动' },
        { status: 403 }
      )
    }
    
    // 检查是否有用户已经领取了邀请码
    if (existingActivity.used_codes > 0) {
      return NextResponse.json(
        { error: '无法删除已有用户领取邀请码的活动，请先停用活动' },
        { status: 409 }
      )
    }
    
    // 删除活动（级联删除会自动处理相关数据）
    const { error: deleteError } = await supabase
      .from('activities')
      .delete()
      .eq('id', params.id)
    
    if (deleteError) {
      console.error('删除活动失败:', deleteError)
      return NextResponse.json(
        { error: '删除活动失败' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      message: `活动 "${existingActivity.title}" 删除成功`
    })
    
  } catch (error) {
    console.error('删除活动错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
