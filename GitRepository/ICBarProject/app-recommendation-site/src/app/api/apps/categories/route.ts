import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'
import { APP_CATEGORIES } from '@/lib/utils/business'

export async function GET() {
  try {
    const supabase = await createClient()
    
    // 获取每个分类的应用数量
    const { data: apps, error } = await supabase
      .from('apps')
      .select('category')
    
    if (error) {
      console.error('获取应用分类失败:', error)
      return NextResponse.json(
        { error: '获取应用分类失败' },
        { status: 500 }
      )
    }
    
    // 统计每个分类的应用数量
    const categoryCounts = apps.reduce((acc, app) => {
      acc[app.category] = (acc[app.category] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    // 组合分类信息和统计数据
    const categoriesWithCounts = APP_CATEGORIES.map(category => ({
      ...category,
      count: categoryCounts[category.id] || 0
    }))
    
    // 添加总计
    const totalApps = apps.length
    const categoriesWithTotal = [
      {
        id: 'all',
        name: '全部分类',
        icon: '📱',
        count: totalApps
      },
      ...categoriesWithCounts
    ]
    
    return NextResponse.json({
      categories: categoriesWithTotal,
      total: totalApps
    })
    
  } catch (error) {
    console.error('获取应用分类错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
