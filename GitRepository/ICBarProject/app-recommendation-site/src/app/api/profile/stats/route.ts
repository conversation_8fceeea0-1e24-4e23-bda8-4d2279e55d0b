import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: '未认证' },
        { status: 401 }
      )
    }
    
    // 并行获取各项统计数据
    const [
      appsResult,
      activitiesResult,
      claimsResult,
      feedbacksResult
    ] = await Promise.all([
      // 用户发布的应用数量
      supabase
        .from('apps')
        .select('id', { count: 'exact', head: true })
        .eq('created_by', user.id),
      
      // 用户创建的活动数量
      supabase
        .from('activities')
        .select('id', { count: 'exact', head: true })
        .eq('created_by', user.id),
      
      // 用户领取的邀请码数量
      supabase
        .from('code_claims')
        .select('id', { count: 'exact', head: true })
        .eq('user_id', user.id),
      
      // 用户提交的反馈数量
      supabase
        .from('code_claims')
        .select('id', { count: 'exact', head: true })
        .eq('user_id', user.id)
        .eq('feedback_submitted', true)
    ])
    
    // 检查是否有错误
    const errors = [
      appsResult.error,
      activitiesResult.error,
      claimsResult.error,
      feedbacksResult.error
    ].filter(Boolean)
    
    if (errors.length > 0) {
      console.error('获取用户统计数据失败:', errors)
      return NextResponse.json(
        { error: '获取统计数据失败' },
        { status: 500 }
      )
    }
    
    const stats = {
      totalApps: appsResult.count || 0,
      totalActivities: activitiesResult.count || 0,
      totalClaims: claimsResult.count || 0,
      totalFeedbacks: feedbacksResult.count || 0
    }
    
    return NextResponse.json({ stats })
    
  } catch (error) {
    console.error('获取用户统计错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
