import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: '未认证' },
        { status: 401 }
      )
    }
    
    // 获取用户创建的活动
    const { data: activities, error: activitiesError } = await supabase
      .from('activities')
      .select(`
        id,
        title,
        description,
        total_codes,
        used_codes,
        is_active,
        expires_at,
        created_at,
        apps (
          id,
          name,
          icon_url
        )
      `)
      .eq('created_by', user.id)
      .order('created_at', { ascending: false })
    
    if (activitiesError) {
      console.error('获取创建活动失败:', activitiesError)
      return NextResponse.json(
        { error: '获取创建活动失败' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      activities: activities || [],
      total: activities?.length || 0
    })
    
  } catch (error) {
    console.error('获取用户创建活动错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
