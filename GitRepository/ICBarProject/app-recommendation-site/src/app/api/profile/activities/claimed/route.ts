import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: '未认证' },
        { status: 401 }
      )
    }
    
    // 获取用户领取的活动记录
    const { data: claims, error: claimsError } = await supabase
      .from('code_claims')
      .select(`
        id,
        invite_code,
        claimed_at,
        feedback_submitted,
        activities (
          id,
          title,
          description,
          feedback_required,
          apps (
            id,
            name,
            icon_url
          )
        )
      `)
      .eq('user_id', user.id)
      .order('claimed_at', { ascending: false })
    
    if (claimsError) {
      console.error('获取领取记录失败:', claimsError)
      return NextResponse.json(
        { error: '获取领取记录失败' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      claims: claims || [],
      total: claims?.length || 0
    })
    
  } catch (error) {
    console.error('获取用户领取活动错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
