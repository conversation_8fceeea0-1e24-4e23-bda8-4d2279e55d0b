import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const startTime = Date.now()
    
    // 检查数据库连接
    const supabase = await createClient()
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1)
      .single()
    
    const dbStatus = error ? 'error' : 'healthy'
    const responseTime = Date.now() - startTime
    
    // 检查环境变量
    const envCheck = {
      supabase_url: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      supabase_anon_key: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      service_role_key: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
      app_url: !!process.env.NEXT_PUBLIC_APP_URL,
    }
    
    const allEnvVarsPresent = Object.values(envCheck).every(<PERSON><PERSON><PERSON>)
    
    const healthData = {
      status: dbStatus === 'healthy' && allEnvVarsPresent ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      checks: {
        database: {
          status: dbStatus,
          responseTime: `${responseTime}ms`,
          error: error?.message || null
        },
        environment: {
          status: allEnvVarsPresent ? 'healthy' : 'error',
          variables: envCheck
        }
      },
      system: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        memory: {
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
          external: Math.round(process.memoryUsage().external / 1024 / 1024)
        }
      }
    }
    
    const statusCode = healthData.status === 'healthy' ? 200 : 503
    
    return NextResponse.json(healthData, { status: statusCode })
    
  } catch (error) {
    console.error('Health check failed:', error)
    
    return NextResponse.json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      checks: {
        database: {
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }
    }, { status: 503 })
  }
}

// 支持HEAD请求用于简单的健康检查
export async function HEAD() {
  try {
    const supabase = await createClient()
    const { error } = await supabase
      .from('users')
      .select('count')
      .limit(1)
      .single()
    
    return new Response(null, { 
      status: error ? 503 : 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
  } catch (error) {
    return new Response(null, { status: 503 })
  }
}
