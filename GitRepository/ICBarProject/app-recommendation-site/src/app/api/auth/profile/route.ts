import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { userProfileSchema } from '@/lib/validations/schemas'

export async function GET() {
  try {
    const supabase = await createClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: '未认证' },
        { status: 401 }
      )
    }
    
    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single()
    
    if (profileError) {
      return NextResponse.json(
        { error: '获取用户资料失败' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        ...profile
      }
    })
    
  } catch (error) {
    console.error('获取用户资料错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: '未认证' },
        { status: 401 }
      )
    }
    
    const body = await request.json()
    
    // 验证输入数据
    const validation = userProfileSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        { error: '输入数据无效', details: validation.error.errors },
        { status: 400 }
      )
    }
    
    const updates = validation.data
    
    // 更新用户资料
    const { data: profile, error: updateError } = await supabase
      .from('users')
      .update(updates)
      .eq('id', user.id)
      .select()
      .single()
    
    if (updateError) {
      if (updateError.code === '23505') {
        return NextResponse.json(
          { error: '用户名已被使用' },
          { status: 409 }
        )
      }
      
      return NextResponse.json(
        { error: '更新用户资料失败' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      message: '用户资料更新成功',
      profile
    })
    
  } catch (error) {
    console.error('更新用户资料错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
