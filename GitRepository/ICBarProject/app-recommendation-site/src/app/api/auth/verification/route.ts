import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { verificationSchema } from '@/lib/validations/schemas'

// 模拟实名认证服务
async function mockVerificationService(data: {
  real_name: string
  id_number: string
  phone: string
}) {
  // 在实际项目中，这里会调用真实的实名认证API
  // 例如：阿里云实人认证、腾讯云人脸核身等
  
  // 模拟验证逻辑
  await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟API调用延迟
  
  // 简单的验证规则（仅用于演示）
  const isValidIdNumber = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(data.id_number)
  const isValidPhone = /^1[3-9]\d{9}$/.test(data.phone)
  const isValidName = data.real_name.length >= 2 && data.real_name.length <= 20
  
  if (!isValidIdNumber || !isValidPhone || !isValidName) {
    throw new Error('身份信息验证失败')
  }
  
  // 模拟成功响应
  return {
    success: true,
    verification_id: `verify_${Date.now()}`,
    confidence_score: 0.95
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: '未认证' },
        { status: 401 }
      )
    }
    
    // 检查用户是否已经认证
    const { data: profile } = await supabase
      .from('users')
      .select('is_verified')
      .eq('id', user.id)
      .single()
    
    if (profile?.is_verified) {
      return NextResponse.json(
        { error: '用户已完成实名认证' },
        { status: 409 }
      )
    }
    
    const body = await request.json()
    
    // 验证输入数据
    const validation = verificationSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        { error: '输入数据无效', details: validation.error.errors },
        { status: 400 }
      )
    }
    
    const verificationData = validation.data
    
    try {
      // 调用实名认证服务
      const verificationResult = await mockVerificationService(verificationData)
      
      if (verificationResult.success) {
        // 更新用户认证状态
        const { error: updateError } = await supabase
          .from('users')
          .update({ 
            is_verified: true,
            full_name: verificationData.real_name
          })
          .eq('id', user.id)
        
        if (updateError) {
          console.error('更新认证状态失败:', updateError)
          return NextResponse.json(
            { error: '更新认证状态失败' },
            { status: 500 }
          )
        }
        
        return NextResponse.json({
          message: '实名认证成功',
          verification_id: verificationResult.verification_id
        })
      } else {
        return NextResponse.json(
          { error: '实名认证失败，请检查身份信息' },
          { status: 400 }
        )
      }
      
    } catch (verificationError) {
      console.error('实名认证服务错误:', verificationError)
      return NextResponse.json(
        { error: verificationError instanceof Error ? verificationError.message : '实名认证服务异常' },
        { status: 400 }
      )
    }
    
  } catch (error) {
    console.error('实名认证错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    const supabase = await createClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: '未认证' },
        { status: 401 }
      )
    }
    
    const { data: profile } = await supabase
      .from('users')
      .select('is_verified, full_name')
      .eq('id', user.id)
      .single()
    
    return NextResponse.json({
      is_verified: profile?.is_verified || false,
      full_name: profile?.full_name
    })
    
  } catch (error) {
    console.error('获取认证状态错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
