import { GET, HEAD } from '@/app/api/health/route'
import { NextRequest } from 'next/server'

// Mock Supabase
const mockSupabaseClient = {
  from: jest.fn(() => ({
    select: jest.fn(() => ({
      limit: jest.fn(() => ({
        single: jest.fn()
      }))
    }))
  }))
}

jest.mock('@/lib/supabase/server', () => ({
  createClient: jest.fn(() => Promise.resolve(mockSupabaseClient))
}))

describe('/api/health', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock environment variables
    process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co'
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key'
    process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-role-key'
    process.env.NEXT_PUBLIC_APP_URL = 'http://localhost:3000'
  })

  describe('GET /api/health', () => {
    it('returns healthy status when database is accessible', async () => {
      // Mock successful database query
      mockSupabaseClient.from().select().limit().single.mockResolvedValue({
        data: { count: 1 },
        error: null
      })

      const request = new NextRequest('http://localhost:3000/api/health')
      const response = await GET()
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.status).toBe('healthy')
      expect(data.checks.database.status).toBe('healthy')
      expect(data.checks.environment.status).toBe('healthy')
    })

    it('returns unhealthy status when database is not accessible', async () => {
      // Mock database error
      mockSupabaseClient.from().select().limit().single.mockResolvedValue({
        data: null,
        error: { message: 'Connection failed' }
      })

      const request = new NextRequest('http://localhost:3000/api/health')
      const response = await GET()
      const data = await response.json()

      expect(response.status).toBe(503)
      expect(data.status).toBe('unhealthy')
      expect(data.checks.database.status).toBe('error')
    })

    it('returns unhealthy status when environment variables are missing', async () => {
      // Remove environment variables
      delete process.env.NEXT_PUBLIC_SUPABASE_URL
      
      mockSupabaseClient.from().select().limit().single.mockResolvedValue({
        data: { count: 1 },
        error: null
      })

      const request = new NextRequest('http://localhost:3000/api/health')
      const response = await GET()
      const data = await response.json()

      expect(response.status).toBe(503)
      expect(data.status).toBe('unhealthy')
      expect(data.checks.environment.status).toBe('error')
      expect(data.checks.environment.variables.supabase_url).toBe(false)
    })

    it('includes system information in response', async () => {
      mockSupabaseClient.from().select().limit().single.mockResolvedValue({
        data: { count: 1 },
        error: null
      })

      const request = new NextRequest('http://localhost:3000/api/health')
      const response = await GET()
      const data = await response.json()

      expect(data.system).toBeDefined()
      expect(data.system.nodeVersion).toBeDefined()
      expect(data.system.platform).toBeDefined()
      expect(data.system.memory).toBeDefined()
      expect(data.uptime).toBeDefined()
      expect(data.timestamp).toBeDefined()
    })
  })

  describe('HEAD /api/health', () => {
    it('returns 200 when database is accessible', async () => {
      mockSupabaseClient.from().select().limit().single.mockResolvedValue({
        data: { count: 1 },
        error: null
      })

      const response = await HEAD()

      expect(response.status).toBe(200)
      expect(response.headers.get('Cache-Control')).toBe('no-cache, no-store, must-revalidate')
    })

    it('returns 503 when database is not accessible', async () => {
      mockSupabaseClient.from().select().limit().single.mockResolvedValue({
        data: null,
        error: { message: 'Connection failed' }
      })

      const response = await HEAD()

      expect(response.status).toBe(503)
    })
  })
})
