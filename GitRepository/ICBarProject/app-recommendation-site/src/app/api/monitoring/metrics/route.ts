import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const metric = await request.json()
    
    // 验证指标数据
    if (!metric.name || typeof metric.value !== 'number') {
      return NextResponse.json(
        { error: '无效的指标数据' },
        { status: 400 }
      )
    }
    
    // 在生产环境中，这里可以将指标发送到监控服务
    // 例如：CloudWatch, DataDog, New Relic等
    
    if (process.env.NODE_ENV === 'development') {
      console.log('收到性能指标:', metric)
    }
    
    // 示例：存储到数据库或发送到外部服务
    // await storeMetric(metric)
    // await sendToMonitoringService(metric)
    
    return NextResponse.json({ success: true })
    
  } catch (error) {
    console.error('处理性能指标失败:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // 返回性能监控摘要
    const summary = {
      status: 'active',
      timestamp: new Date().toISOString(),
      metrics: {
        collected: true,
        endpoints: [
          '/api/monitoring/metrics',
          '/api/health'
        ]
      }
    }
    
    return NextResponse.json(summary)
    
  } catch (error) {
    console.error('获取监控状态失败:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
