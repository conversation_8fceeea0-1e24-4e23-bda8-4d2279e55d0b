import Link from 'next/link'
import { 
  ExclamationTriangleIcon,
  HomeIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline'

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          {/* 404图标 */}
          <div className="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-red-100 mb-6">
            <ExclamationTriangleIcon className="h-12 w-12 text-red-600" />
          </div>
          
          {/* 404标题 */}
          <h1 className="text-6xl font-bold text-gray-900 mb-4">404</h1>
          <h2 className="text-2xl font-semibold text-gray-700 mb-4">页面未找到</h2>
          
          {/* 描述 */}
          <p className="text-gray-600 mb-8 max-w-md mx-auto">
            抱歉，您访问的页面不存在或已被移动。请检查URL是否正确，或返回首页继续浏览。
          </p>
          
          {/* 操作按钮 */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <HomeIcon className="w-5 h-5 mr-2" />
              返回首页
            </Link>
            
            <button
              onClick={() => window.history.back()}
              className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <ArrowLeftIcon className="w-5 h-5 mr-2" />
              返回上页
            </button>
          </div>
        </div>
      </div>
      
      {/* 帮助链接 */}
      <div className="mt-12 text-center">
        <div className="text-sm text-gray-500 mb-4">
          您可能在寻找：
        </div>
        <div className="flex flex-wrap justify-center gap-4 text-sm">
          <Link href="/apps" className="text-blue-600 hover:text-blue-800">
            应用列表
          </Link>
          <Link href="/activities" className="text-blue-600 hover:text-blue-800">
            送码活动
          </Link>
          <Link href="/profile" className="text-blue-600 hover:text-blue-800">
            个人中心
          </Link>
          <Link href="/contact" className="text-blue-600 hover:text-blue-800">
            联系我们
          </Link>
        </div>
      </div>
    </div>
  )
}
