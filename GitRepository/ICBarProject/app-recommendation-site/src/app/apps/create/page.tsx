'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { appSchema } from '@/lib/validations/schemas'
import { APP_CATEGORIES } from '@/lib/utils/business'
import { RequireAuth } from '@/components/auth/AuthProvider'
import { PhotoIcon } from '@heroicons/react/24/outline'
import { z } from 'zod'

type AppFormData = z.infer<typeof appSchema>

export default function CreateAppPage() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch
  } = useForm<AppFormData>({
    resolver: zodResolver(appSchema)
  })
  
  const watchedIconUrl = watch('icon_url')
  
  const onSubmit = async (data: AppFormData) => {
    setLoading(true)
    setError('')
    
    try {
      const response = await fetch('/api/apps', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || '创建应用失败')
      }
      
      const result = await response.json()
      
      // 跳转到应用详情页
      router.push(`/apps/${result.app.id}`)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : '创建应用失败')
    } finally {
      setLoading(false)
    }
  }
  
  return (
    <RequireAuth>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* 页面头部 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">发布应用</h1>
            <p className="mt-2 text-gray-600">
              分享您的应用，让更多用户发现并参与送码活动
            </p>
          </div>
          
          {/* 表单 */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
              {error && (
                <div className="rounded-md bg-red-50 p-4">
                  <div className="text-sm text-red-700">{error}</div>
                </div>
              )}
              
              {/* 应用名称 */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  应用名称 *
                </label>
                <input
                  type="text"
                  id="name"
                  {...register('name')}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请输入应用名称"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>
              
              {/* 应用描述 */}
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                  应用描述 *
                </label>
                <textarea
                  id="description"
                  rows={4}
                  {...register('description')}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="详细描述您的应用功能和特色..."
                />
                {errors.description && (
                  <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
                )}
              </div>
              
              {/* 应用图标 */}
              <div>
                <label htmlFor="icon_url" className="block text-sm font-medium text-gray-700 mb-2">
                  应用图标
                </label>
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    {watchedIconUrl ? (
                      <img
                        src={watchedIconUrl}
                        alt="应用图标预览"
                        className="w-16 h-16 rounded-lg object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement
                          target.style.display = 'none'
                          target.nextElementSibling?.classList.remove('hidden')
                        }}
                      />
                    ) : null}
                    <div className={`w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center ${watchedIconUrl ? 'hidden' : ''}`}>
                      <PhotoIcon className="w-8 h-8 text-gray-400" />
                    </div>
                  </div>
                  <div className="flex-1">
                    <input
                      type="url"
                      id="icon_url"
                      {...register('icon_url')}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="https://example.com/icon.png"
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      请提供应用图标的URL地址，建议尺寸为512x512像素
                    </p>
                  </div>
                </div>
                {errors.icon_url && (
                  <p className="mt-1 text-sm text-red-600">{errors.icon_url.message}</p>
                )}
              </div>
              
              {/* 应用分类 */}
              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                  应用分类 *
                </label>
                <select
                  id="category"
                  {...register('category')}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">请选择分类</option>
                  {APP_CATEGORIES.filter(cat => cat.id !== 'all').map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.icon} {category.name}
                    </option>
                  ))}
                </select>
                {errors.category && (
                  <p className="mt-1 text-sm text-red-600">{errors.category.message}</p>
                )}
              </div>
              
              {/* 开发者 */}
              <div>
                <label htmlFor="developer" className="block text-sm font-medium text-gray-700 mb-2">
                  开发者 *
                </label>
                <input
                  type="text"
                  id="developer"
                  {...register('developer')}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请输入开发者名称"
                />
                {errors.developer && (
                  <p className="mt-1 text-sm text-red-600">{errors.developer.message}</p>
                )}
              </div>
              
              {/* 官网地址 */}
              <div>
                <label htmlFor="website_url" className="block text-sm font-medium text-gray-700 mb-2">
                  官网地址
                </label>
                <input
                  type="url"
                  id="website_url"
                  {...register('website_url')}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="https://example.com"
                />
                {errors.website_url && (
                  <p className="mt-1 text-sm text-red-600">{errors.website_url.message}</p>
                )}
              </div>
              
              {/* 下载地址 */}
              <div>
                <label htmlFor="download_url" className="block text-sm font-medium text-gray-700 mb-2">
                  下载地址
                </label>
                <input
                  type="url"
                  id="download_url"
                  {...register('download_url')}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="https://apps.apple.com/app/... 或 https://play.google.com/store/apps/..."
                />
                {errors.download_url && (
                  <p className="mt-1 text-sm text-red-600">{errors.download_url.message}</p>
                )}
              </div>
              
              {/* 提交按钮 */}
              <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => router.back()}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? '发布中...' : '发布应用'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </RequireAuth>
  )
}
