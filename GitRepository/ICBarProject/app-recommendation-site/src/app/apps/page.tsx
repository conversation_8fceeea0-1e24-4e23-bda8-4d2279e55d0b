import { createClient } from '@/lib/supabase/server'
import AppList from '@/components/apps/AppList'
import Link from 'next/link'
import { PlusIcon } from '@heroicons/react/24/outline'

interface PageProps {
  searchParams: {
    query?: string
    category?: string
    sort?: string
    page?: string
  }
}

export default async function AppsPage({ searchParams }: PageProps) {
  const supabase = await createClient()
  
  // 解析查询参数
  const query = searchParams.query || ''
  const category = searchParams.category || 'all'
  const sortBy = searchParams.sort || 'hot_score'
  const page = parseInt(searchParams.page || '1')
  const limit = 20
  const offset = (page - 1) * limit
  
  // 构建应用查询
  let appsQuery = supabase
    .from('apps')
    .select(`
      *,
      users:created_by (
        username,
        full_name,
        is_verified,
        is_vip
      )
    `)
  
  // 添加搜索条件
  if (query) {
    appsQuery = appsQuery.or(`name.ilike.%${query}%,description.ilike.%${query}%,developer.ilike.%${query}%`)
  }
  
  if (category && category !== 'all') {
    appsQuery = appsQuery.eq('category', category)
  }
  
  // 排序
  const sortColumn = sortBy === 'hot_score' ? 'hot_score' : 
                    sortBy === 'activity_count' ? 'activity_count' : 'created_at'
  appsQuery = appsQuery.order(sortColumn, { ascending: false })
  
  // 分页
  appsQuery = appsQuery.range(offset, offset + limit - 1)
  
  // 获取应用列表
  const { data: apps, error: appsError } = await appsQuery
  
  if (appsError) {
    console.error('获取应用列表失败:', appsError)
  }
  
  // 获取分类列表
  const { data: categoriesData, error: categoriesError } = await supabase
    .from('apps')
    .select('category')
  
  if (categoriesError) {
    console.error('获取分类列表失败:', categoriesError)
  }
  
  // 统计每个分类的应用数量
  const categoryCounts = (categoriesData || []).reduce((acc, app) => {
    acc[app.category] = (acc[app.category] || 0) + 1
    return acc
  }, {} as Record<string, number>)
  
  // 预定义的分类列表
  const APP_CATEGORIES = [
    { id: 'all', name: '全部分类', icon: '📱' },
    { id: 'productivity', name: '效率工具', icon: '⚡' },
    { id: 'social', name: '社交网络', icon: '👥' },
    { id: 'entertainment', name: '娱乐休闲', icon: '🎮' },
    { id: 'education', name: '教育学习', icon: '📚' },
    { id: 'finance', name: '金融理财', icon: '💰' },
    { id: 'health', name: '健康医疗', icon: '🏥' },
    { id: 'shopping', name: '购物消费', icon: '🛒' },
    { id: 'travel', name: '旅行出行', icon: '✈️' },
    { id: 'news', name: '新闻资讯', icon: '📰' },
    { id: 'photography', name: '摄影图像', icon: '📸' },
    { id: 'music', name: '音乐音频', icon: '🎵' },
    { id: 'video', name: '视频播放', icon: '🎬' },
    { id: 'tools', name: '实用工具', icon: '🔧' },
    { id: 'other', name: '其他', icon: '📱' }
  ]
  
  const categories = APP_CATEGORIES.map(cat => ({
    ...cat,
    count: cat.id === 'all' ? (categoriesData?.length || 0) : (categoryCounts[cat.id] || 0)
  }))
  
  // 获取总数（用于分页）
  let countQuery = supabase
    .from('apps')
    .select('*', { count: 'exact', head: true })
  
  if (query) {
    countQuery = countQuery.or(`name.ilike.%${query}%,description.ilike.%${query}%,developer.ilike.%${query}%`)
  }
  
  if (category && category !== 'all') {
    countQuery = countQuery.eq('category', category)
  }
  
  const { count } = await countQuery
  
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面头部 */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">应用推荐</h1>
            <p className="mt-2 text-gray-600">
              发现优质应用，参与送码活动获取邀请码
            </p>
          </div>
          
          <Link
            href="/apps/create"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <PlusIcon className="w-5 h-5 mr-2" />
            发布应用
          </Link>
        </div>
        
        {/* 统计信息 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                  <span className="text-blue-600 text-lg">📱</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">总应用数</p>
                <p className="text-2xl font-semibold text-gray-900">{count || 0}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                  <span className="text-green-600 text-lg">🎯</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">活跃应用</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {apps?.filter(app => app.activity_count > 0).length || 0}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center">
                  <span className="text-purple-600 text-lg">🏷️</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">应用分类</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {categories.filter(cat => cat.count > 0 && cat.id !== 'all').length}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-orange-100 rounded-md flex items-center justify-center">
                  <span className="text-orange-600 text-lg">🔥</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">平均热度</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {apps && apps.length > 0 
                    ? (apps.reduce((sum, app) => sum + app.hot_score, 0) / apps.length).toFixed(1)
                    : '0.0'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
        
        {/* 应用列表 */}
        <AppList 
          initialApps={apps || []}
          initialCategories={categories}
          showSearch={true}
          showFilters={true}
        />
      </div>
    </div>
  )
}

export const metadata = {
  title: '应用推荐 - 发现优质应用',
  description: '发现优质应用，参与送码活动获取邀请码'
}
