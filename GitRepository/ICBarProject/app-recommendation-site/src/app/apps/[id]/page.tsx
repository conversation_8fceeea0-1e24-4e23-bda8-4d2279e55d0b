import { notFound } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { createClient } from '@/lib/supabase/server'
import { getCategoryInfo, formatTimeAgo, isActivityActive } from '@/lib/utils/business'
import CreateActivityButton from '@/components/apps/CreateActivityButton'
import { StarIcon, FireIcon, ClockIcon, UserIcon, GlobeAltIcon, ArrowDownTrayIcon } from '@heroicons/react/24/outline'
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid'

interface PageProps {
  params: { id: string }
}

export default async function AppDetailPage({ params }: PageProps) {
  const supabase = await createClient()
  
  // 获取应用详情
  const { data: app, error } = await supabase
    .from('apps')
    .select(`
      *,
      users:created_by (
        username,
        full_name,
        is_verified,
        is_vip
      ),
      activities (
        id,
        title,
        description,
        total_codes,
        used_codes,
        is_active,
        expires_at,
        created_at,
        requires_verification,
        requires_subscription,
        feedback_required
      )
    `)
    .eq('id', params.id)
    .single()
  
  if (error || !app) {
    notFound()
  }
  
  const categoryInfo = getCategoryInfo(app.category)
  
  // 计算统计数据
  const activeActivities = app.activities?.filter(activity => isActivityActive(activity)) || []
  const totalCodes = app.activities?.reduce((sum, activity) => sum + activity.total_codes, 0) || 0
  const usedCodes = app.activities?.reduce((sum, activity) => sum + activity.used_codes, 0) || 0
  const claimRate = totalCodes > 0 ? (usedCodes / totalCodes) * 100 : 0
  
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 面包屑导航 */}
        <nav className="flex mb-8" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <Link href="/" className="text-gray-700 hover:text-blue-600">
                首页
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <span className="mx-2 text-gray-400">/</span>
                <Link href="/apps" className="text-gray-700 hover:text-blue-600">
                  应用
                </Link>
              </div>
            </li>
            <li>
              <div className="flex items-center">
                <span className="mx-2 text-gray-400">/</span>
                <span className="text-gray-500">{app.name}</span>
              </div>
            </li>
          </ol>
        </nav>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 主要内容 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 应用基本信息 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-start space-x-6">
                {/* 应用图标 */}
                <div className="flex-shrink-0">
                  {app.icon_url ? (
                    <Image
                      src={app.icon_url}
                      alt={app.name}
                      width={96}
                      height={96}
                      className="rounded-xl"
                    />
                  ) : (
                    <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center text-white text-3xl font-bold">
                      {app.name.charAt(0).toUpperCase()}
                    </div>
                  )}
                </div>
                
                {/* 应用信息 */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-3 mb-2">
                    <h1 className="text-3xl font-bold text-gray-900">{app.name}</h1>
                    
                    {/* VIP标识 */}
                    {app.users?.is_vip && (
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                        <StarIconSolid className="w-4 h-4 mr-1" />
                        VIP开发者
                      </span>
                    )}
                    
                    {/* 认证标识 */}
                    {app.users?.is_verified && (
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        已认证
                      </span>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-6 text-sm text-gray-500 mb-4">
                    <span className="flex items-center">
                      <span className="mr-2">{categoryInfo.icon}</span>
                      {categoryInfo.name}
                    </span>
                    
                    <span className="flex items-center">
                      <UserIcon className="w-4 h-4 mr-1" />
                      {app.developer}
                    </span>
                    
                    <span className="flex items-center">
                      <ClockIcon className="w-4 h-4 mr-1" />
                      {formatTimeAgo(app.created_at)}
                    </span>
                  </div>
                  
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    {app.description}
                  </p>
                  
                  {/* 操作按钮 */}
                  <div className="flex space-x-4">
                    {app.download_url && (
                      <a
                        href={app.download_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <ArrowDownTrayIcon className="w-5 h-5 mr-2" />
                        下载应用
                      </a>
                    )}
                    
                    {app.website_url && (
                      <a
                        href={app.website_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center px-6 py-3 border border-gray-300 shadow-sm text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <GlobeAltIcon className="w-5 h-5 mr-2" />
                        访问官网
                      </a>
                    )}
                  </div>
                </div>
              </div>
            </div>
            
            {/* 送码活动列表 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900">送码活动</h2>
                <CreateActivityButton
                  appId={app.id}
                  appCreatedBy={app.created_by}
                />
              </div>
              
              {activeActivities.length > 0 ? (
                <div className="space-y-4">
                  {activeActivities.map((activity) => (
                    <div key={activity.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="text-lg font-medium text-gray-900 mb-2">
                            {activity.title}
                          </h3>
                          <p className="text-gray-600 mb-3">{activity.description}</p>
                          
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <span>总数: {activity.total_codes}</span>
                            <span>已领: {activity.used_codes}</span>
                            <span>剩余: {activity.total_codes - activity.used_codes}</span>
                            {activity.expires_at && (
                              <span>截止: {new Date(activity.expires_at).toLocaleDateString()}</span>
                            )}
                          </div>
                          
                          {/* 活动条件 */}
                          {(activity.requires_verification || activity.requires_subscription || activity.feedback_required) && (
                            <div className="mt-2 flex flex-wrap gap-2">
                              {activity.requires_verification && (
                                <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                  需要实名认证
                                </span>
                              )}
                              {activity.requires_subscription && (
                                <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                  需要订阅应用
                                </span>
                              )}
                              {activity.feedback_required && (
                                <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
                                  需要反馈
                                </span>
                              )}
                            </div>
                          )}
                        </div>
                        
                        <div className="ml-4">
                          <Link
                            href={`/activities/${activity.id}`}
                            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                          >
                            参与活动
                          </Link>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="text-gray-500 mb-2">暂无活跃的送码活动</div>
                  <div className="text-gray-400 text-sm">请关注后续活动更新</div>
                </div>
              )}
            </div>
          </div>
          
          {/* 侧边栏 */}
          <div className="space-y-6">
            {/* 统计信息 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">应用统计</h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="flex items-center text-gray-600">
                    <FireIcon className="w-5 h-5 mr-2 text-orange-500" />
                    热度分数
                  </span>
                  <span className="font-semibold text-orange-600">{app.hot_score}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="flex items-center text-gray-600">
                    <StarIcon className="w-5 h-5 mr-2 text-blue-500" />
                    总活动数
                  </span>
                  <span className="font-semibold">{app.activity_count}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">活跃活动</span>
                  <span className="font-semibold text-green-600">{activeActivities.length}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">总邀请码</span>
                  <span className="font-semibold">{totalCodes}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">已领取</span>
                  <span className="font-semibold">{usedCodes}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">领取率</span>
                  <span className="font-semibold">{claimRate.toFixed(1)}%</span>
                </div>
              </div>
            </div>
            
            {/* 开发者信息 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">开发者信息</h3>
              
              <div className="space-y-3">
                <div>
                  <span className="text-gray-600">开发者:</span>
                  <span className="ml-2 font-medium">{app.developer}</span>
                </div>
                
                <div>
                  <span className="text-gray-600">发布者:</span>
                  <span className="ml-2 font-medium">
                    {app.users?.full_name || app.users?.username || '匿名用户'}
                  </span>
                </div>
                
                <div>
                  <span className="text-gray-600">发布时间:</span>
                  <span className="ml-2">{new Date(app.created_at).toLocaleDateString()}</span>
                </div>
                
                <div>
                  <span className="text-gray-600">最后更新:</span>
                  <span className="ml-2">{new Date(app.updated_at).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
