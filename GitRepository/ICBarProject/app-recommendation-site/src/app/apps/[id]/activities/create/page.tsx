'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { activitySchema } from '@/lib/validations/schemas'
import { RequireAuth, useAuth } from '@/components/auth/AuthProvider'
import { 
  PlusIcon, 
  MinusIcon, 
  GiftIcon, 
  ExclamationTriangleIcon,
  InformationCircleIcon 
} from '@heroicons/react/24/outline'
import { z } from 'zod'
import Link from 'next/link'

type ActivityFormData = z.infer<typeof activitySchema>

interface App {
  id: string
  name: string
  icon_url?: string
  category: string
  developer: string
  created_by: string
}

export default function CreateActivityPage() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [app, setApp] = useState<App | null>(null)
  const [userPermissions, setUserPermissions] = useState({
    isVip: false,
    canCreateAdvanced: false
  })
  
  const router = useRouter()
  const params = useParams()
  const { user } = useAuth()
  const appId = params.id as string

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    watch,
    setValue
  } = useForm<ActivityFormData>({
    resolver: zodResolver(activitySchema),
    defaultValues: {
      app_id: appId,
      invite_codes: [''],
      requires_verification: false,
      requires_subscription: false,
      required_apps: [],
      feedback_required: false,
      feedback_questions: [],
      is_active: true
    }
  })

  const { fields: codeFields, append: appendCode, remove: removeCode } = useFieldArray({
    control,
    name: 'invite_codes'
  })

  const { fields: questionFields, append: appendQuestion, remove: removeQuestion } = useFieldArray({
    control,
    name: 'feedback_questions'
  })

  const watchRequiresFeedback = watch('feedback_required')
  const watchRequiresVerification = watch('requires_verification')
  const watchRequiresSubscription = watch('requires_subscription')

  // 获取应用信息和用户权限
  useEffect(() => {
    const fetchAppAndPermissions = async () => {
      try {
        // 获取应用信息
        const appResponse = await fetch(`/api/apps/${appId}`)
        if (!appResponse.ok) {
          throw new Error('应用不存在')
        }
        const appData = await appResponse.json()
        setApp(appData.app)

        // 检查权限
        if (user && appData.app.created_by !== user.id) {
          throw new Error('您没有权限为此应用创建活动')
        }

        // 获取用户权限信息
        if (user) {
          const permResponse = await fetch('/api/auth/permissions')
          if (permResponse.ok) {
            const permData = await permResponse.json()
            setUserPermissions({
              isVip: permData.isVip,
              canCreateAdvanced: permData.canCreateAdvancedActivity
            })
          }
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : '加载失败')
      }
    }

    if (appId && user) {
      fetchAppAndPermissions()
    }
  }, [appId, user])

  const onSubmit = async (data: ActivityFormData) => {
    setLoading(true)
    setError('')

    try {
      // 过滤空的邀请码
      const filteredCodes = data.invite_codes.filter(code => code.trim() !== '')
      if (filteredCodes.length === 0) {
        throw new Error('请至少添加一个邀请码')
      }

      // 过滤空的反馈问题
      const filteredQuestions = data.feedback_questions?.filter(q => q.trim() !== '') || []

      const activityData = {
        ...data,
        invite_codes: filteredCodes,
        feedback_questions: data.feedback_required ? filteredQuestions : []
      }

      const response = await fetch('/api/activities', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(activityData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || '创建活动失败')
      }

      const result = await response.json()
      
      // 跳转到活动详情页
      router.push(`/activities/${result.activity.id}`)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : '创建活动失败')
    } finally {
      setLoading(false)
    }
  }

  if (error && !app) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <ExclamationTriangleIcon className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">加载失败</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Link
            href="/apps"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            返回应用列表
          </Link>
        </div>
      </div>
    )
  }

  return (
    <RequireAuth>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* 页面头部 */}
          <div className="mb-8">
            <div className="flex items-center space-x-4 mb-4">
              {app?.icon_url && (
                <img
                  src={app.icon_url}
                  alt={app.name}
                  className="w-12 h-12 rounded-lg object-cover"
                />
              )}
              <div>
                <h1 className="text-3xl font-bold text-gray-900">创建送码活动</h1>
                <p className="text-gray-600">
                  为 <span className="font-medium">{app?.name}</span> 创建送码活动
                </p>
              </div>
            </div>
            
            {/* VIP功能提示 */}
            {!userPermissions.isVip && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start">
                  <InformationCircleIcon className="w-5 h-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" />
                  <div>
                    <h3 className="text-sm font-medium text-blue-800">升级VIP解锁高级功能</h3>
                    <p className="text-sm text-blue-700 mt-1">
                      VIP用户可以设置实名认证要求、应用订阅条件和反馈问卷等高级活动条件
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 表单 */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-8">
              {error && (
                <div className="rounded-md bg-red-50 p-4">
                  <div className="text-sm text-red-700">{error}</div>
                </div>
              )}

              {/* 基本信息 */}
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                  基本信息
                </h3>

                {/* 活动标题 */}
                <div>
                  <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                    活动标题 *
                  </label>
                  <input
                    type="text"
                    id="title"
                    {...register('title')}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入活动标题，如：限时免费邀请码"
                  />
                  {errors.title && (
                    <p className="mt-1 text-sm text-red-600">{errors.title.message}</p>
                  )}
                </div>

                {/* 活动描述 */}
                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                    活动描述 *
                  </label>
                  <textarea
                    id="description"
                    rows={4}
                    {...register('description')}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="详细描述活动内容、使用方法和注意事项..."
                  />
                  {errors.description && (
                    <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
                  )}
                </div>

                {/* 过期时间 */}
                <div>
                  <label htmlFor="expires_at" className="block text-sm font-medium text-gray-700 mb-2">
                    过期时间（可选）
                  </label>
                  <input
                    type="datetime-local"
                    id="expires_at"
                    {...register('expires_at')}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  {errors.expires_at && (
                    <p className="mt-1 text-sm text-red-600">{errors.expires_at.message}</p>
                  )}
                  <p className="mt-1 text-sm text-gray-500">
                    不设置则活动永久有效（直到邀请码用完）
                  </p>
                </div>
              </div>

              {/* 邀请码设置 */}
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                  邀请码设置
                </h3>

                <div>
                  <div className="flex items-center justify-between mb-4">
                    <label className="block text-sm font-medium text-gray-700">
                      邀请码列表 *
                    </label>
                    <button
                      type="button"
                      onClick={() => appendCode('')}
                      className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
                    >
                      <PlusIcon className="w-4 h-4 mr-1" />
                      添加邀请码
                    </button>
                  </div>

                  <div className="space-y-3">
                    {codeFields.map((field, index) => (
                      <div key={field.id} className="flex items-center space-x-3">
                        <div className="flex-1">
                          <input
                            type="text"
                            {...register(`invite_codes.${index}` as const)}
                            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 font-mono"
                            placeholder={`邀请码 ${index + 1}`}
                          />
                        </div>
                        {codeFields.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeCode(index)}
                            className="inline-flex items-center p-2 border border-gray-300 rounded-md text-gray-400 hover:text-red-500 hover:border-red-300"
                          >
                            <MinusIcon className="w-4 h-4" />
                          </button>
                        )}
                      </div>
                    ))}
                  </div>

                  {errors.invite_codes && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.invite_codes.message || '请填写有效的邀请码'}
                    </p>
                  )}

                  <p className="mt-2 text-sm text-gray-500">
                    每行一个邀请码，建议使用随机字符串。总数量将自动计算。
                  </p>
                </div>
              </div>

              {/* 高级条件设置 (VIP功能) */}
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                  高级条件设置
                  {!userPermissions.isVip && (
                    <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      VIP功能
                    </span>
                  )}
                </h3>

                {/* 实名认证要求 */}
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="requires_verification"
                      type="checkbox"
                      {...register('requires_verification')}
                      disabled={!userPermissions.isVip}
                      className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded disabled:opacity-50"
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="requires_verification" className="font-medium text-gray-700">
                      要求实名认证
                    </label>
                    <p className="text-gray-500">只有通过实名认证的用户才能参与此活动</p>
                  </div>
                </div>

                {/* 应用订阅要求 */}
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="requires_subscription"
                      type="checkbox"
                      {...register('requires_subscription')}
                      disabled={!userPermissions.isVip}
                      className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded disabled:opacity-50"
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="requires_subscription" className="font-medium text-gray-700">
                      要求订阅应用
                    </label>
                    <p className="text-gray-500">用户需要订阅此应用才能参与活动</p>
                  </div>
                </div>

                {/* 反馈要求 */}
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="flex items-center h-5">
                      <input
                        id="feedback_required"
                        type="checkbox"
                        {...register('feedback_required')}
                        disabled={!userPermissions.isVip}
                        className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded disabled:opacity-50"
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="feedback_required" className="font-medium text-gray-700">
                        要求提交反馈
                      </label>
                      <p className="text-gray-500">用户使用邀请码后需要提交使用反馈</p>
                    </div>
                  </div>

                  {/* 反馈问题设置 */}
                  {watchRequiresFeedback && userPermissions.isVip && (
                    <div className="ml-7 space-y-3">
                      <div className="flex items-center justify-between">
                        <label className="block text-sm font-medium text-gray-700">
                          反馈问题
                        </label>
                        <button
                          type="button"
                          onClick={() => appendQuestion('')}
                          className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
                        >
                          <PlusIcon className="w-4 h-4 mr-1" />
                          添加问题
                        </button>
                      </div>

                      <div className="space-y-2">
                        {questionFields.map((field, index) => (
                          <div key={field.id} className="flex items-center space-x-3">
                            <div className="flex-1">
                              <input
                                type="text"
                                {...register(`feedback_questions.${index}` as const)}
                                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                placeholder={`问题 ${index + 1}`}
                              />
                            </div>
                            {questionFields.length > 0 && (
                              <button
                                type="button"
                                onClick={() => removeQuestion(index)}
                                className="inline-flex items-center p-2 border border-gray-300 rounded-md text-gray-400 hover:text-red-500 hover:border-red-300"
                              >
                                <MinusIcon className="w-4 h-4" />
                              </button>
                            )}
                          </div>
                        ))}
                      </div>

                      <p className="text-sm text-gray-500">
                        设置用户需要回答的反馈问题，有助于收集使用体验
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* 提交按钮 */}
              <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <Link
                  href={`/apps/${appId}`}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  取消
                </Link>
                <button
                  type="submit"
                  disabled={loading}
                  className="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      创建中...
                    </>
                  ) : (
                    <>
                      <GiftIcon className="w-4 h-4 mr-2" />
                      创建活动
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </RequireAuth>
  )
}
