import { createClient } from '@/lib/supabase/server'
import ActivityCard from '@/components/activities/ActivityCard'
import Link from 'next/link'
import { GiftIcon, PlusIcon } from '@heroicons/react/24/outline'

interface PageProps {
  searchParams: {
    app_id?: string
    is_active?: string
    page?: string
  }
}

export default async function ActivitiesPage({ searchParams }: PageProps) {
  const supabase = await createClient()
  
  // 解析查询参数
  const appId = searchParams.app_id
  const isActive = searchParams.is_active === 'true' ? true : searchParams.is_active === 'false' ? false : undefined
  const page = parseInt(searchParams.page || '1')
  const limit = 20
  const offset = (page - 1) * limit
  
  // 构建活动查询
  let activitiesQuery = supabase
    .from('activities')
    .select(`
      *,
      apps (
        id,
        name,
        icon_url,
        category,
        developer
      ),
      users:created_by (
        username,
        full_name,
        is_verified,
        is_vip
      )
    `)
  
  // 添加筛选条件
  if (appId) {
    activitiesQuery = activitiesQuery.eq('app_id', appId)
  }
  
  if (isActive !== undefined) {
    activitiesQuery = activitiesQuery.eq('is_active', isActive)
  }
  
  // 排序（活跃的活动优先，然后按创建时间）
  activitiesQuery = activitiesQuery.order('is_active', { ascending: false })
  activitiesQuery = activitiesQuery.order('created_at', { ascending: false })
  
  // 分页
  activitiesQuery = activitiesQuery.range(offset, offset + limit - 1)
  
  // 获取活动列表
  const { data: activities, error: activitiesError } = await activitiesQuery
  
  if (activitiesError) {
    console.error('获取活动列表失败:', activitiesError)
  }
  
  // 获取总数（用于分页）
  let countQuery = supabase
    .from('activities')
    .select('*', { count: 'exact', head: true })
  
  if (appId) {
    countQuery = countQuery.eq('app_id', appId)
  }
  
  if (isActive !== undefined) {
    countQuery = countQuery.eq('is_active', isActive)
  }
  
  const { count } = await countQuery
  
  // 获取统计数据
  const { count: totalActivities } = await supabase
    .from('activities')
    .select('*', { count: 'exact', head: true })
  
  const { count: activeActivities } = await supabase
    .from('activities')
    .select('*', { count: 'exact', head: true })
    .eq('is_active', true)
  
  const { data: totalCodesData } = await supabase
    .from('activities')
    .select('total_codes, used_codes')
  
  const totalCodes = totalCodesData?.reduce((sum, activity) => sum + activity.total_codes, 0) || 0
  const usedCodes = totalCodesData?.reduce((sum, activity) => sum + activity.used_codes, 0) || 0
  
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面头部 */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">送码活动</h1>
            <p className="mt-2 text-gray-600">
              参与送码活动，获取应用邀请码和优惠
            </p>
          </div>
          
          <Link
            href="/apps"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <PlusIcon className="w-5 h-5 mr-2" />
            发布应用创建活动
          </Link>
        </div>
        
        {/* 统计信息 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                  <GiftIcon className="w-5 h-5 text-blue-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">总活动数</p>
                <p className="text-2xl font-semibold text-gray-900">{totalActivities || 0}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                  <span className="text-green-600 text-lg">🎯</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">活跃活动</p>
                <p className="text-2xl font-semibold text-gray-900">{activeActivities || 0}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center">
                  <span className="text-purple-600 text-lg">🎫</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">总邀请码</p>
                <p className="text-2xl font-semibold text-gray-900">{totalCodes}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-orange-100 rounded-md flex items-center justify-center">
                  <span className="text-orange-600 text-lg">📊</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">领取率</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {totalCodes > 0 ? ((usedCodes / totalCodes) * 100).toFixed(1) : '0.0'}%
                </p>
              </div>
            </div>
          </div>
        </div>
        
        {/* 筛选器 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700">筛选:</span>
              <Link
                href="/activities"
                className={`px-3 py-1 rounded-full text-sm font-medium ${
                  !isActive && !appId
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                全部活动
              </Link>
              <Link
                href="/activities?is_active=true"
                className={`px-3 py-1 rounded-full text-sm font-medium ${
                  isActive === true
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                活跃活动
              </Link>
              <Link
                href="/activities?is_active=false"
                className={`px-3 py-1 rounded-full text-sm font-medium ${
                  isActive === false
                    ? 'bg-gray-100 text-gray-800'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                已结束
              </Link>
            </div>
          </div>
        </div>
        
        {/* 活动列表 */}
        {activities && activities.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {activities.map((activity) => (
              <ActivityCard key={activity.id} activity={activity} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <GiftIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无活动</h3>
            <p className="text-gray-500 mb-6">
              {isActive === true 
                ? '当前没有活跃的送码活动' 
                : isActive === false 
                ? '没有已结束的活动'
                : '还没有送码活动，成为第一个创建者吧！'
              }
            </p>
            <Link
              href="/apps/create"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <PlusIcon className="w-5 h-5 mr-2" />
              发布应用创建活动
            </Link>
          </div>
        )}
        
        {/* 分页 */}
        {count && count > limit && (
          <div className="mt-8 flex justify-center">
            <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
              {page > 1 && (
                <Link
                  href={`/activities?${new URLSearchParams({
                    ...searchParams,
                    page: (page - 1).toString()
                  }).toString()}`}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                >
                  上一页
                </Link>
              )}
              
              {Array.from({ length: Math.ceil(count / limit) }, (_, i) => i + 1).map((pageNum) => (
                <Link
                  key={pageNum}
                  href={`/activities?${new URLSearchParams({
                    ...searchParams,
                    page: pageNum.toString()
                  }).toString()}`}
                  className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                    pageNum === page
                      ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                      : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                  }`}
                >
                  {pageNum}
                </Link>
              ))}
              
              {page < Math.ceil(count / limit) && (
                <Link
                  href={`/activities?${new URLSearchParams({
                    ...searchParams,
                    page: (page + 1).toString()
                  }).toString()}`}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                >
                  下一页
                </Link>
              )}
            </nav>
          </div>
        )}
      </div>
    </div>
  )
}

export const metadata = {
  title: '送码活动 - 获取应用邀请码',
  description: '参与送码活动，获取应用邀请码和优惠'
}
