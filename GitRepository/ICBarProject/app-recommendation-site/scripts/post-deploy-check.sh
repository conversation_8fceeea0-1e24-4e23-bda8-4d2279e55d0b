#!/bin/bash

# 部署后验证脚本
# 用于验证应用部署是否成功

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
APP_URL="${NEXT_PUBLIC_APP_URL:-http://localhost:3000}"
TIMEOUT=30
MAX_RETRIES=5

echo -e "${BLUE}🚀 开始部署后验证...${NC}"
echo -e "${BLUE}应用URL: $APP_URL${NC}"

# 等待应用启动
wait_for_app() {
    echo -e "${YELLOW}⏳ 等待应用启动...${NC}"
    
    for i in $(seq 1 $MAX_RETRIES); do
        if curl -f -s "$APP_URL" > /dev/null; then
            echo -e "${GREEN}✅ 应用已启动${NC}"
            return 0
        fi
        
        echo -e "${YELLOW}尝试 $i/$MAX_RETRIES 失败，等待5秒后重试...${NC}"
        sleep 5
    done
    
    echo -e "${RED}❌ 应用启动失败${NC}"
    return 1
}

# 健康检查
health_check() {
    echo -e "${YELLOW}🔍 执行健康检查...${NC}"
    
    response=$(curl -s -w "%{http_code}" "$APP_URL/api/health")
    http_code="${response: -3}"
    body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ 健康检查通过${NC}"
        
        # 解析健康检查响应
        if command -v jq &> /dev/null; then
            status=$(echo "$body" | jq -r '.status')
            if [ "$status" = "healthy" ]; then
                echo -e "${GREEN}✅ 应用状态: $status${NC}"
            else
                echo -e "${RED}❌ 应用状态异常: $status${NC}"
                return 1
            fi
        fi
        
        return 0
    else
        echo -e "${RED}❌ 健康检查失败 (HTTP $http_code)${NC}"
        echo -e "${RED}响应: $body${NC}"
        return 1
    fi
}

# 关键页面检查
check_pages() {
    echo -e "${YELLOW}📄 检查关键页面...${NC}"
    
    pages=(
        "/"
        "/apps"
        "/activities"
        "/login"
        "/register"
    )
    
    for page in "${pages[@]}"; do
        url="$APP_URL$page"
        echo -e "${BLUE}检查页面: $url${NC}"
        
        response=$(curl -s -w "%{http_code}" "$url")
        http_code="${response: -3}"
        
        if [ "$http_code" = "200" ]; then
            echo -e "${GREEN}✅ $page 页面正常${NC}"
        else
            echo -e "${RED}❌ $page 页面异常 (HTTP $http_code)${NC}"
            return 1
        fi
    done
}

# API端点检查
check_api_endpoints() {
    echo -e "${YELLOW}🔌 检查API端点...${NC}"
    
    endpoints=(
        "/api/health"
        "/api/apps"
        "/api/activities"
    )
    
    for endpoint in "${endpoints[@]}"; do
        url="$APP_URL$endpoint"
        echo -e "${BLUE}检查API: $url${NC}"
        
        response=$(curl -s -w "%{http_code}" "$url")
        http_code="${response: -3}"
        
        if [ "$http_code" = "200" ] || [ "$http_code" = "401" ]; then
            echo -e "${GREEN}✅ $endpoint API正常${NC}"
        else
            echo -e "${RED}❌ $endpoint API异常 (HTTP $http_code)${NC}"
            return 1
        fi
    done
}

# 性能检查
performance_check() {
    echo -e "${YELLOW}⚡ 执行性能检查...${NC}"
    
    # 检查首页加载时间
    start_time=$(date +%s%N)
    curl -s "$APP_URL" > /dev/null
    end_time=$(date +%s%N)
    
    load_time=$(( (end_time - start_time) / 1000000 )) # 转换为毫秒
    
    echo -e "${BLUE}首页加载时间: ${load_time}ms${NC}"
    
    if [ $load_time -lt 3000 ]; then
        echo -e "${GREEN}✅ 页面加载性能良好${NC}"
    elif [ $load_time -lt 5000 ]; then
        echo -e "${YELLOW}⚠️ 页面加载较慢，建议优化${NC}"
    else
        echo -e "${RED}❌ 页面加载过慢，需要优化${NC}"
        return 1
    fi
}

# 数据库连接检查
database_check() {
    echo -e "${YELLOW}🗄️ 检查数据库连接...${NC}"
    
    response=$(curl -s "$APP_URL/api/health")
    
    if command -v jq &> /dev/null; then
        db_status=$(echo "$response" | jq -r '.checks.database.status')
        if [ "$db_status" = "healthy" ]; then
            echo -e "${GREEN}✅ 数据库连接正常${NC}"
        else
            echo -e "${RED}❌ 数据库连接异常${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}⚠️ 无法解析数据库状态（缺少jq工具）${NC}"
    fi
}

# 环境变量检查
env_check() {
    echo -e "${YELLOW}🔧 检查环境配置...${NC}"
    
    response=$(curl -s "$APP_URL/api/health")
    
    if command -v jq &> /dev/null; then
        env_status=$(echo "$response" | jq -r '.checks.environment.status')
        if [ "$env_status" = "healthy" ]; then
            echo -e "${GREEN}✅ 环境配置正常${NC}"
        else
            echo -e "${RED}❌ 环境配置异常${NC}"
            echo -e "${RED}请检查环境变量设置${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}⚠️ 无法解析环境状态（缺少jq工具）${NC}"
    fi
}

# 安全检查
security_check() {
    echo -e "${YELLOW}🔒 执行安全检查...${NC}"
    
    # 检查HTTPS
    if [[ $APP_URL == https://* ]]; then
        echo -e "${GREEN}✅ 使用HTTPS连接${NC}"
    else
        echo -e "${YELLOW}⚠️ 未使用HTTPS连接${NC}"
    fi
    
    # 检查安全头
    headers=$(curl -s -I "$APP_URL")
    
    if echo "$headers" | grep -i "x-frame-options" > /dev/null; then
        echo -e "${GREEN}✅ X-Frame-Options 头存在${NC}"
    else
        echo -e "${YELLOW}⚠️ 缺少 X-Frame-Options 头${NC}"
    fi
    
    if echo "$headers" | grep -i "x-content-type-options" > /dev/null; then
        echo -e "${GREEN}✅ X-Content-Type-Options 头存在${NC}"
    else
        echo -e "${YELLOW}⚠️ 缺少 X-Content-Type-Options 头${NC}"
    fi
}

# 生成报告
generate_report() {
    echo -e "${BLUE}📊 生成验证报告...${NC}"
    
    report_file="deployment-verification-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$report_file" << EOF
部署验证报告
=============

部署时间: $(date)
应用URL: $APP_URL
验证状态: $1

检查项目:
- 应用启动: ✅
- 健康检查: ✅
- 关键页面: ✅
- API端点: ✅
- 性能检查: ✅
- 数据库连接: ✅
- 环境配置: ✅
- 安全检查: ✅

备注: $2
EOF
    
    echo -e "${GREEN}✅ 报告已生成: $report_file${NC}"
}

# 主函数
main() {
    echo -e "${BLUE}开始执行部署后验证检查...${NC}"
    
    # 执行所有检查
    if wait_for_app && \
       health_check && \
       check_pages && \
       check_api_endpoints && \
       performance_check && \
       database_check && \
       env_check; then
        
        security_check # 安全检查不影响整体结果
        
        echo -e "${GREEN}🎉 所有检查通过！部署验证成功！${NC}"
        generate_report "成功" "所有检查项目均通过"
        return 0
    else
        echo -e "${RED}❌ 部署验证失败！请检查上述错误并修复。${NC}"
        generate_report "失败" "存在检查项目未通过"
        return 1
    fi
}

# 帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -u, --url URL  指定应用URL"
    echo "  -t, --timeout  设置超时时间（秒）"
    echo ""
    echo "环境变量:"
    echo "  NEXT_PUBLIC_APP_URL  应用URL（默认: http://localhost:3000）"
    echo ""
    echo "示例:"
    echo "  $0                                    # 使用默认配置"
    echo "  $0 -u https://myapp.vercel.app       # 指定应用URL"
    echo "  NEXT_PUBLIC_APP_URL=https://myapp.vercel.app $0  # 使用环境变量"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -u|--url)
            APP_URL="$2"
            shift 2
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main
