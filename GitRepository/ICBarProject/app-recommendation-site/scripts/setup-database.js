#!/usr/bin/env node

/**
 * 数据库设置脚本
 * 用于初始化Supabase数据库架构和种子数据
 */

const fs = require('fs')
const path = require('path')
const { createClient } = require('@supabase/supabase-js')

// 从环境变量读取Supabase配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ 请设置 NEXT_PUBLIC_SUPABASE_URL 和 SUPABASE_SERVICE_ROLE_KEY 环境变量')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function executeSqlFile(filePath, description) {
  try {
    console.log(`📄 执行 ${description}...`)
    
    const sql = fs.readFileSync(filePath, 'utf8')
    
    // 分割SQL语句（简单的分割，可能需要更复杂的解析）
    const statements = sql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))
    
    for (const statement of statements) {
      if (statement.trim()) {
        const { error } = await supabase.rpc('exec_sql', { sql_query: statement })
        if (error) {
          console.warn(`⚠️  SQL执行警告: ${error.message}`)
          // 某些错误可能是预期的（如表已存在），继续执行
        }
      }
    }
    
    console.log(`✅ ${description} 完成`)
  } catch (error) {
    console.error(`❌ ${description} 失败:`, error.message)
    throw error
  }
}

async function checkConnection() {
  try {
    console.log('🔗 检查Supabase连接...')
    const { data, error } = await supabase.from('_test').select('*').limit(1)
    
    if (error && error.code !== 'PGRST116') { // PGRST116 = table not found, 这是预期的
      throw error
    }
    
    console.log('✅ Supabase连接正常')
  } catch (error) {
    console.error('❌ Supabase连接失败:', error.message)
    throw error
  }
}

async function setupDatabase() {
  try {
    await checkConnection()
    
    // 执行架构文件
    const schemaPath = path.join(__dirname, '../supabase/schema.sql')
    if (fs.existsSync(schemaPath)) {
      await executeSqlFile(schemaPath, '数据库架构')
    } else {
      console.warn('⚠️  未找到 schema.sql 文件')
    }
    
    // 询问是否要加载种子数据
    const shouldLoadSeed = process.argv.includes('--seed') || process.argv.includes('-s')
    
    if (shouldLoadSeed) {
      const seedPath = path.join(__dirname, '../supabase/seed.sql')
      if (fs.existsSync(seedPath)) {
        console.log('🌱 加载种子数据...')
        await executeSqlFile(seedPath, '种子数据')
      } else {
        console.warn('⚠️  未找到 seed.sql 文件')
      }
    } else {
      console.log('ℹ️  跳过种子数据加载（使用 --seed 参数来加载种子数据）')
    }
    
    console.log('\n🎉 数据库设置完成！')
    console.log('\n📋 下一步:')
    console.log('1. 在Supabase控制台中验证表结构')
    console.log('2. 配置RLS策略（如果需要）')
    console.log('3. 创建测试用户账号')
    console.log('4. 启动开发服务器: npm run dev')
    
  } catch (error) {
    console.error('\n💥 数据库设置失败:', error.message)
    process.exit(1)
  }
}

async function resetDatabase() {
  try {
    console.log('🗑️  重置数据库...')
    
    // 删除所有表（按依赖顺序）
    const dropTables = [
      'DROP TABLE IF EXISTS public.code_claims CASCADE;',
      'DROP TABLE IF EXISTS public.user_subscriptions CASCADE;',
      'DROP TABLE IF EXISTS public.activities CASCADE;',
      'DROP TABLE IF EXISTS public.apps CASCADE;',
      'DROP TABLE IF EXISTS public.users CASCADE;'
    ]
    
    for (const sql of dropTables) {
      const { error } = await supabase.rpc('exec_sql', { sql_query: sql })
      if (error) {
        console.warn(`⚠️  删除表警告: ${error.message}`)
      }
    }
    
    console.log('✅ 数据库重置完成')
    
    // 重新设置
    await setupDatabase()
    
  } catch (error) {
    console.error('❌ 数据库重置失败:', error.message)
    process.exit(1)
  }
}

// 主函数
async function main() {
  const command = process.argv[2]
  
  switch (command) {
    case 'setup':
      await setupDatabase()
      break
    case 'reset':
      await resetDatabase()
      break
    case 'seed':
      const seedPath = path.join(__dirname, '../supabase/seed.sql')
      if (fs.existsSync(seedPath)) {
        await executeSqlFile(seedPath, '种子数据')
      }
      break
    default:
      console.log('📖 用法:')
      console.log('  node scripts/setup-database.js setup [--seed]  # 设置数据库架构')
      console.log('  node scripts/setup-database.js reset          # 重置并重新设置数据库')
      console.log('  node scripts/setup-database.js seed           # 仅加载种子数据')
      console.log('')
      console.log('📝 环境变量:')
      console.log('  NEXT_PUBLIC_SUPABASE_URL      # Supabase项目URL')
      console.log('  SUPABASE_SERVICE_ROLE_KEY     # Supabase服务角色密钥')
      break
  }
}

// 运行主函数
if (require.main === module) {
  main().catch(console.error)
}

module.exports = {
  setupDatabase,
  resetDatabase,
  executeSqlFile
}
