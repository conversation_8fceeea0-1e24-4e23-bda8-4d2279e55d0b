#!/bin/bash

# 部署脚本
# 用于自动化部署流程

set -e  # 遇到错误时退出

echo "🚀 开始部署应用推荐网站..."

# 检查环境变量
check_env_vars() {
    echo "📋 检查环境变量..."
    
    required_vars=(
        "NEXT_PUBLIC_SUPABASE_URL"
        "NEXT_PUBLIC_SUPABASE_ANON_KEY"
        "SUPABASE_SERVICE_ROLE_KEY"
        "NEXT_PUBLIC_APP_URL"
    )
    
    missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        echo "❌ 缺少以下环境变量:"
        printf '%s\n' "${missing_vars[@]}"
        echo "请在 .env.local 文件中设置这些变量"
        exit 1
    fi
    
    echo "✅ 环境变量检查通过"
}

# 安装依赖
install_dependencies() {
    echo "📦 安装依赖..."
    npm ci
    echo "✅ 依赖安装完成"
}

# 运行类型检查
type_check() {
    echo "🔍 运行TypeScript类型检查..."
    npm run type-check
    echo "✅ 类型检查通过"
}

# 运行代码检查
lint_check() {
    echo "🔍 运行代码检查..."
    npm run lint
    echo "✅ 代码检查通过"
}

# 构建应用
build_app() {
    echo "🏗️  构建应用..."
    npm run build
    echo "✅ 应用构建完成"
}

# 数据库健康检查
check_database() {
    echo "🗄️  检查数据库连接..."
    
    # 这里可以添加数据库连接检查
    # 例如运行一个简单的查询来验证连接
    
    echo "✅ 数据库连接正常"
}

# 部署到Vercel
deploy_to_vercel() {
    echo "🚀 部署到Vercel..."
    
    if command -v vercel &> /dev/null; then
        vercel --prod
        echo "✅ 部署到Vercel完成"
    else
        echo "⚠️  Vercel CLI未安装，请手动部署或安装Vercel CLI"
        echo "安装命令: npm i -g vercel"
    fi
}

# 部署后验证
post_deploy_check() {
    echo "🔍 部署后验证..."

    if [ -n "$NEXT_PUBLIC_APP_URL" ]; then
        echo "运行完整的部署后验证脚本..."

        # 运行详细的验证脚本
        if bash scripts/post-deploy-check.sh -u "$NEXT_PUBLIC_APP_URL"; then
            echo "✅ 部署后验证完全通过"
        else
            echo "❌ 部署后验证失败，请检查问题"
            return 1
        fi
    else
        echo "⚠️  未设置APP_URL，跳过部署后验证"
    fi
}

# 主函数
main() {
    echo "🎯 部署环境: ${NODE_ENV:-development}"
    
    # 检查是否在正确的目录
    if [ ! -f "package.json" ]; then
        echo "❌ 请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 执行部署步骤
    check_env_vars
    install_dependencies
    type_check
    lint_check
    build_app
    check_database
    
    # 根据环境选择部署方式
    if [ "$NODE_ENV" = "production" ]; then
        deploy_to_vercel
        post_deploy_check
    else
        echo "🔧 开发环境，跳过部署步骤"
        echo "如需部署，请设置 NODE_ENV=production"
    fi
    
    echo "🎉 部署流程完成！"
}

# 帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -p, --prod     生产环境部署"
    echo "  -d, --dev      开发环境构建"
    echo ""
    echo "环境变量:"
    echo "  NODE_ENV                      部署环境 (development/production)"
    echo "  NEXT_PUBLIC_SUPABASE_URL      Supabase项目URL"
    echo "  NEXT_PUBLIC_SUPABASE_ANON_KEY Supabase匿名密钥"
    echo "  SUPABASE_SERVICE_ROLE_KEY     Supabase服务角色密钥"
    echo "  NEXT_PUBLIC_APP_URL           应用URL"
    echo ""
    echo "示例:"
    echo "  $0 --prod                     生产环境部署"
    echo "  NODE_ENV=production $0        生产环境部署"
}

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    -p|--prod)
        export NODE_ENV=production
        main
        ;;
    -d|--dev)
        export NODE_ENV=development
        main
        ;;
    "")
        main
        ;;
    *)
        echo "未知选项: $1"
        show_help
        exit 1
        ;;
esac
