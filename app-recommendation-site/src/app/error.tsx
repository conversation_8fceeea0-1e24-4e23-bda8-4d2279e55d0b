'use client'

import { useEffect } from 'react'
import Link from 'next/link'
import { 
  ExclamationCircleIcon,
  HomeIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // 记录错误到监控服务
    console.error('应用错误:', error)
  }, [error])

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          {/* 错误图标 */}
          <div className="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-red-100 mb-6">
            <ExclamationCircleIcon className="h-12 w-12 text-red-600" />
          </div>
          
          {/* 错误标题 */}
          <h1 className="text-3xl font-bold text-gray-900 mb-4">出现了一些问题</h1>
          
          {/* 错误描述 */}
          <p className="text-gray-600 mb-8 max-w-md mx-auto">
            抱歉，应用遇到了意外错误。我们已经记录了这个问题，请稍后重试。
          </p>
          
          {/* 错误详情（开发环境显示） */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mb-8 p-4 bg-gray-100 rounded-lg text-left">
              <h3 className="text-sm font-medium text-gray-900 mb-2">错误详情：</h3>
              <pre className="text-xs text-gray-700 overflow-auto">
                {error.message}
              </pre>
              {error.digest && (
                <p className="text-xs text-gray-500 mt-2">
                  错误ID: {error.digest}
                </p>
              )}
            </div>
          )}
          
          {/* 操作按钮 */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={reset}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <ArrowPathIcon className="w-5 h-5 mr-2" />
              重试
            </button>
            
            <Link
              href="/"
              className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <HomeIcon className="w-5 h-5 mr-2" />
              返回首页
            </Link>
          </div>
        </div>
      </div>
      
      {/* 帮助信息 */}
      <div className="mt-12 text-center">
        <div className="text-sm text-gray-500 mb-4">
          如果问题持续存在，请联系我们：
        </div>
        <div className="flex flex-wrap justify-center gap-4 text-sm">
          <Link href="/contact" className="text-blue-600 hover:text-blue-800">
            联系客服
          </Link>
          <Link href="/help" className="text-blue-600 hover:text-blue-800">
            帮助中心
          </Link>
          <a 
            href="mailto:<EMAIL>" 
            className="text-blue-600 hover:text-blue-800"
          >
            发送邮件
          </a>
        </div>
      </div>
    </div>
  )
}
