'use client'

import { useState, useEffect } from 'react'
import { RequireAuth } from '@/components/auth/AuthProvider'
import { 
  GiftIcon, 
  ClockIcon, 
  CheckCircleIcon,
  ExclamationTriangleIcon,
  EyeIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link'

interface ActivityClaim {
  id: string
  invite_code: string
  claimed_at: string
  feedback_submitted: boolean
  activities: {
    id: string
    title: string
    description: string
    feedback_required: boolean
    apps: {
      id: string
      name: string
      icon_url?: string
    }
  }
}

interface CreatedActivity {
  id: string
  title: string
  description: string
  total_codes: number
  used_codes: number
  is_active: boolean
  expires_at?: string
  created_at: string
  apps: {
    id: string
    name: string
    icon_url?: string
  }
}

export default function UserActivitiesPage() {
  const [activeTab, setActiveTab] = useState<'claimed' | 'created'>('claimed')
  const [claimedActivities, setClaimedActivities] = useState<ActivityClaim[]>([])
  const [createdActivities, setCreatedActivities] = useState<CreatedActivity[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    const fetchActivities = async () => {
      try {
        setLoading(true)
        
        // 获取用户领取的活动
        const claimedResponse = await fetch('/api/profile/activities/claimed')
        if (claimedResponse.ok) {
          const claimedData = await claimedResponse.json()
          setClaimedActivities(claimedData.claims || [])
        }
        
        // 获取用户创建的活动
        const createdResponse = await fetch('/api/profile/activities/created')
        if (createdResponse.ok) {
          const createdData = await createdResponse.json()
          setCreatedActivities(createdData.activities || [])
        }
        
      } catch (err) {
        setError(err instanceof Error ? err.message : '加载失败')
      } finally {
        setLoading(false)
      }
    }

    fetchActivities()
  }, [])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getActivityStatus = (activity: CreatedActivity) => {
    if (!activity.is_active) {
      return { status: 'inactive', label: '已停用', color: 'text-gray-600 bg-gray-100' }
    }
    
    if (activity.expires_at && new Date(activity.expires_at) <= new Date()) {
      return { status: 'expired', label: '已过期', color: 'text-red-600 bg-red-100' }
    }
    
    if (activity.used_codes >= activity.total_codes) {
      return { status: 'full', label: '已领完', color: 'text-yellow-600 bg-yellow-100' }
    }
    
    return { status: 'active', label: '进行中', color: 'text-green-600 bg-green-100' }
  }

  return (
    <RequireAuth>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* 页面头部 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">我的活动</h1>
            <p className="mt-2 text-gray-600">
              查看您参与和创建的送码活动
            </p>
          </div>

          {/* 标签页 */}
          <div className="mb-8">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8">
                <button
                  onClick={() => setActiveTab('claimed')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'claimed'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <GiftIcon className="w-5 h-5 inline mr-2" />
                  已领取活动 ({claimedActivities.length})
                </button>
                <button
                  onClick={() => setActiveTab('created')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'created'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <ClockIcon className="w-5 h-5 inline mr-2" />
                  创建的活动 ({createdActivities.length})
                </button>
              </nav>
            </div>
          </div>

          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-4 text-gray-600">加载中...</p>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <ExclamationTriangleIcon className="w-12 h-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">加载失败</h3>
              <p className="text-gray-600">{error}</p>
            </div>
          ) : (
            <div className="space-y-6">
              {/* 已领取活动 */}
              {activeTab === 'claimed' && (
                <div>
                  {claimedActivities.length > 0 ? (
                    <div className="grid grid-cols-1 gap-6">
                      {claimedActivities.map((claim) => (
                        <div key={claim.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                          <div className="flex items-start justify-between">
                            <div className="flex items-start space-x-4 flex-1">
                              {claim.activities.apps.icon_url && (
                                <img
                                  src={claim.activities.apps.icon_url}
                                  alt={claim.activities.apps.name}
                                  className="w-12 h-12 rounded-lg object-cover flex-shrink-0"
                                />
                              )}
                              <div className="flex-1 min-w-0">
                                <h3 className="text-lg font-medium text-gray-900 mb-1">
                                  {claim.activities.title}
                                </h3>
                                <p className="text-sm text-gray-600 mb-2">
                                  应用：{claim.activities.apps.name}
                                </p>
                                <p className="text-gray-600 mb-3 line-clamp-2">
                                  {claim.activities.description}
                                </p>
                                
                                <div className="flex items-center space-x-4 text-sm text-gray-500">
                                  <span>邀请码：{claim.invite_code}</span>
                                  <span>领取时间：{formatDate(claim.claimed_at)}</span>
                                  {claim.activities.feedback_required && (
                                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                      claim.feedback_submitted 
                                        ? 'bg-green-100 text-green-800' 
                                        : 'bg-yellow-100 text-yellow-800'
                                    }`}>
                                      {claim.feedback_submitted ? '已反馈' : '待反馈'}
                                    </span>
                                  )}
                                </div>
                              </div>
                            </div>
                            
                            <div className="flex items-center space-x-3">
                              <Link
                                href={`/activities/${claim.activities.id}`}
                                className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                              >
                                <EyeIcon className="w-4 h-4 mr-2" />
                                查看详情
                              </Link>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <GiftIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">暂无领取记录</h3>
                      <p className="text-gray-600 mb-6">您还没有领取过任何送码活动</p>
                      <Link
                        href="/activities"
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                      >
                        浏览活动
                      </Link>
                    </div>
                  )}
                </div>
              )}

              {/* 创建的活动 */}
              {activeTab === 'created' && (
                <div>
                  {createdActivities.length > 0 ? (
                    <div className="grid grid-cols-1 gap-6">
                      {createdActivities.map((activity) => {
                        const status = getActivityStatus(activity)
                        const remainingCodes = activity.total_codes - activity.used_codes
                        const claimRate = activity.total_codes > 0 ? (activity.used_codes / activity.total_codes) * 100 : 0

                        return (
                          <div key={activity.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <div className="flex items-start justify-between">
                              <div className="flex items-start space-x-4 flex-1">
                                {activity.apps.icon_url && (
                                  <img
                                    src={activity.apps.icon_url}
                                    alt={activity.apps.name}
                                    className="w-12 h-12 rounded-lg object-cover flex-shrink-0"
                                  />
                                )}
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center space-x-3 mb-2">
                                    <h3 className="text-lg font-medium text-gray-900">
                                      {activity.title}
                                    </h3>
                                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${status.color}`}>
                                      {status.label}
                                    </span>
                                  </div>

                                  <p className="text-sm text-gray-600 mb-2">
                                    应用：{activity.apps.name}
                                  </p>
                                  <p className="text-gray-600 mb-4 line-clamp-2">
                                    {activity.description}
                                  </p>

                                  {/* 统计信息 */}
                                  <div className="grid grid-cols-4 gap-4 mb-4">
                                    <div className="text-center">
                                      <div className="text-lg font-semibold text-gray-900">{activity.total_codes}</div>
                                      <div className="text-xs text-gray-500">总邀请码</div>
                                    </div>
                                    <div className="text-center">
                                      <div className="text-lg font-semibold text-blue-600">{activity.used_codes}</div>
                                      <div className="text-xs text-gray-500">已领取</div>
                                    </div>
                                    <div className="text-center">
                                      <div className="text-lg font-semibold text-green-600">{remainingCodes}</div>
                                      <div className="text-xs text-gray-500">剩余</div>
                                    </div>
                                    <div className="text-center">
                                      <div className="text-lg font-semibold text-purple-600">{claimRate.toFixed(0)}%</div>
                                      <div className="text-xs text-gray-500">领取率</div>
                                    </div>
                                  </div>

                                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                                    <span>创建时间：{formatDate(activity.created_at)}</span>
                                    {activity.expires_at && (
                                      <span>过期时间：{formatDate(activity.expires_at)}</span>
                                    )}
                                  </div>
                                </div>
                              </div>

                              <div className="flex items-center space-x-3">
                                <Link
                                  href={`/activities/${activity.id}`}
                                  className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                                >
                                  <EyeIcon className="w-4 h-4 mr-2" />
                                  查看详情
                                </Link>
                                <Link
                                  href={`/profile/activities/${activity.id}/manage`}
                                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                                >
                                  管理
                                </Link>
                              </div>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <ClockIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">暂无创建记录</h3>
                      <p className="text-gray-600 mb-6">您还没有创建过任何送码活动</p>
                      <Link
                        href="/apps"
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                      >
                        发布应用创建活动
                      </Link>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </RequireAuth>
  )
}
