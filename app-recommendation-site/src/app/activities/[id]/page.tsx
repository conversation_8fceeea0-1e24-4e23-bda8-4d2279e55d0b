import { notFound } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { createClient } from '@/lib/supabase/server'
import { formatTimeAgo, formatExpiryTime, getActivityStatus } from '@/lib/utils/business'
import <PERSON>laimCodeForm from '@/components/activities/ClaimCodeForm'
import ActivityFeedbackSection from '@/components/activities/ActivityFeedbackSection'
import { ClockIcon, UserIcon, GiftIcon, CheckCircleIcon, XCircleIcon, StarIcon } from '@heroicons/react/24/outline'
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid'

interface PageProps {
  params: { id: string }
}

export default async function ActivityDetailPage({ params }: PageProps) {
  const supabase = await createClient()
  
  // 获取活动详情
  const { data: activity, error } = await supabase
    .from('activities')
    .select(`
      *,
      apps (
        id,
        name,
        icon_url,
        category,
        developer,
        website_url,
        download_url,
        created_by
      ),
      users:created_by (
        username,
        full_name,
        is_verified,
        is_vip
      )
    `)
    .eq('id', params.id)
    .single()
  
  if (error || !activity) {
    notFound()
  }
  
  const activityStatus = getActivityStatus(activity)
  const remainingCodes = activity.total_codes - activity.used_codes
  const claimRate = activity.total_codes > 0 ? (activity.used_codes / activity.total_codes) * 100 : 0
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-100'
      case 'expired':
        return 'text-red-600 bg-red-100'
      case 'full':
        return 'text-yellow-600 bg-yellow-100'
      case 'inactive':
        return 'text-gray-600 bg-gray-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }
  
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 面包屑导航 */}
        <nav className="flex mb-8" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <Link href="/" className="text-gray-700 hover:text-blue-600">
                首页
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <span className="mx-2 text-gray-400">/</span>
                <Link href="/activities" className="text-gray-700 hover:text-blue-600">
                  活动
                </Link>
              </div>
            </li>
            <li>
              <div className="flex items-center">
                <span className="mx-2 text-gray-400">/</span>
                <span className="text-gray-500">{activity.title}</span>
              </div>
            </li>
          </ol>
        </nav>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 主要内容 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 活动基本信息 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h1 className="text-3xl font-bold text-gray-900">{activity.title}</h1>
                    
                    {/* VIP标识 */}
                    {activity.users?.is_vip && (
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                        <StarIconSolid className="w-4 h-4 mr-1" />
                        VIP活动
                      </span>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-6 text-sm text-gray-500 mb-4">
                    <span className="flex items-center">
                      <UserIcon className="w-4 h-4 mr-1" />
                      {activity.users?.full_name || activity.users?.username || '匿名用户'}
                      {activity.users?.is_verified && (
                        <span className="ml-1 text-green-600">✓</span>
                      )}
                    </span>
                    
                    <span className="flex items-center">
                      <ClockIcon className="w-4 h-4 mr-1" />
                      {formatTimeAgo(activity.created_at)}
                    </span>
                  </div>
                </div>
                
                {/* 状态标识 */}
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(activityStatus.status)}`}>
                  {activityStatus.status === 'active' && <CheckCircleIcon className="w-4 h-4 mr-1" />}
                  {activityStatus.status === 'expired' && <XCircleIcon className="w-4 h-4 mr-1" />}
                  {activityStatus.status === 'full' && <GiftIcon className="w-4 h-4 mr-1" />}
                  {activityStatus.message}
                </span>
              </div>
              
              <p className="text-gray-600 mb-6 leading-relaxed">
                {activity.description}
              </p>
              
              {/* 关联应用 */}
              {activity.apps && (
                <div className="bg-gray-50 rounded-lg p-4 mb-6">
                  <h3 className="text-sm font-medium text-gray-900 mb-3">关联应用</h3>
                  <div className="flex items-center space-x-4">
                    {activity.apps.icon_url ? (
                      <Image
                        src={activity.apps.icon_url}
                        alt={activity.apps.name}
                        width={64}
                        height={64}
                        className="rounded-lg"
                      />
                    ) : (
                      <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white text-xl font-bold">
                        {activity.apps.name.charAt(0).toUpperCase()}
                      </div>
                    )}
                    
                    <div className="flex-1">
                      <Link
                        href={`/apps/${activity.apps.id}`}
                        className="text-lg font-medium text-gray-900 hover:text-blue-600 transition-colors"
                      >
                        {activity.apps.name}
                      </Link>
                      <p className="text-gray-600">{activity.apps.developer}</p>
                      
                      <div className="flex space-x-4 mt-2">
                        {activity.apps.website_url && (
                          <a
                            href={activity.apps.website_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-sm text-blue-600 hover:text-blue-800"
                          >
                            官网
                          </a>
                        )}
                        {activity.apps.download_url && (
                          <a
                            href={activity.apps.download_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-sm text-blue-600 hover:text-blue-800"
                          >
                            下载
                          </a>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              {/* 活动条件 */}
              {(activity.requires_verification || activity.requires_subscription || activity.feedback_required) && (
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-gray-900 mb-3">参与条件</h3>
                  <div className="space-y-2">
                    {activity.requires_verification && (
                      <div className="flex items-center text-sm text-blue-700 bg-blue-50 px-3 py-2 rounded-lg">
                        <CheckCircleIcon className="w-4 h-4 mr-2" />
                        需要完成实名认证
                      </div>
                    )}
                    {activity.requires_subscription && (
                      <div className="flex items-center text-sm text-purple-700 bg-purple-50 px-3 py-2 rounded-lg">
                        <StarIcon className="w-4 h-4 mr-2" />
                        需要订阅指定应用
                      </div>
                    )}
                    {activity.feedback_required && (
                      <div className="flex items-center text-sm text-green-700 bg-green-50 px-3 py-2 rounded-lg">
                        <GiftIcon className="w-4 h-4 mr-2" />
                        使用后需要提交反馈
                      </div>
                    )}
                  </div>
                </div>
              )}
              
              {/* 反馈问题 */}
              {activity.feedback_required && activity.feedback_questions && activity.feedback_questions.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-gray-900 mb-3">反馈问题</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <ol className="list-decimal list-inside space-y-2 text-sm text-gray-600">
                      {activity.feedback_questions.map((question, index) => (
                        <li key={index}>{question}</li>
                      ))}
                    </ol>
                  </div>
                </div>
              )}
            </div>
            
            {/* 领取邀请码表单 */}
            {activityStatus.status === 'active' && (
              <ClaimCodeForm activityId={activity.id} />
            )}

            {/* 反馈功能 */}
            <ActivityFeedbackSection
              activityId={activity.id}
              feedbackRequired={activity.feedback_required}
              feedbackQuestions={activity.feedback_questions || []}
            />
          </div>
          
          {/* 侧边栏 */}
          <div className="space-y-6">
            {/* 活动统计 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">活动统计</h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">总邀请码</span>
                  <span className="font-semibold text-lg">{activity.total_codes}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">已领取</span>
                  <span className="font-semibold text-lg text-blue-600">{activity.used_codes}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">剩余</span>
                  <span className="font-semibold text-lg text-green-600">{remainingCodes}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">领取率</span>
                  <span className="font-semibold text-lg">{claimRate.toFixed(1)}%</span>
                </div>
                
                {/* 进度条 */}
                <div className="pt-2">
                  <div className="flex justify-between text-xs text-gray-500 mb-1">
                    <span>进度</span>
                    <span>{activity.used_codes}/{activity.total_codes}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${claimRate}%` }}
                    />
                  </div>
                </div>
              </div>
            </div>
            
            {/* 活动信息 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">活动信息</h3>
              
              <div className="space-y-3">
                <div>
                  <span className="text-gray-600">创建时间:</span>
                  <span className="ml-2">{new Date(activity.created_at).toLocaleString()}</span>
                </div>
                
                <div>
                  <span className="text-gray-600">最后更新:</span>
                  <span className="ml-2">{new Date(activity.updated_at).toLocaleString()}</span>
                </div>
                
                {activity.expires_at && (
                  <div>
                    <span className="text-gray-600">截止时间:</span>
                    <span className={`ml-2 ${new Date(activity.expires_at) <= new Date() ? 'text-red-600' : 'text-gray-900'}`}>
                      {new Date(activity.expires_at).toLocaleString()}
                    </span>
                  </div>
                )}
                
                <div>
                  <span className="text-gray-600">活动状态:</span>
                  <span className={`ml-2 font-medium ${
                    activityStatus.status === 'active' ? 'text-green-600' :
                    activityStatus.status === 'expired' ? 'text-red-600' :
                    activityStatus.status === 'full' ? 'text-yellow-600' :
                    'text-gray-600'
                  }`}>
                    {activityStatus.message}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
