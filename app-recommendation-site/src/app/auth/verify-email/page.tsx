'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { 
  EnvelopeIcon, 
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link'

export default function VerifyEmailPage() {
  const [email, setEmail] = useState('')
  const [resending, setResending] = useState(false)
  const [resent, setResent] = useState(false)
  const [error, setError] = useState('')
  const searchParams = useSearchParams()

  useEffect(() => {
    const emailParam = searchParams.get('email')
    if (emailParam) {
      setEmail(decodeURIComponent(emailParam))
    }
  }, [searchParams])

  const handleResendEmail = async () => {
    if (!email) return

    setResending(true)
    setError('')

    try {
      const response = await fetch('/api/auth/resend-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || '重发邮件失败')
      }

      setResent(true)
    } catch (err) {
      setError(err instanceof Error ? err.message : '重发邮件失败')
    } finally {
      setResending(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
            <EnvelopeIcon className="w-8 h-8 text-blue-600" />
          </div>
        </div>
        <h2 className="mt-6 text-center text-3xl font-bold text-gray-900">
          验证您的邮箱
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          我们已向您的邮箱发送了验证链接
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            <div className="mb-6">
              <div className="inline-flex items-center px-4 py-2 bg-blue-50 rounded-lg">
                <EnvelopeIcon className="w-5 h-5 text-blue-600 mr-2" />
                <span className="text-sm font-medium text-blue-900">
                  {email || '您的邮箱'}
                </span>
              </div>
            </div>

            <div className="space-y-4 text-sm text-gray-600">
              <p>
                请检查您的邮箱（包括垃圾邮件文件夹），点击验证链接完成账户激活。
              </p>
              
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-start">
                  <ExclamationTriangleIcon className="w-5 h-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0" />
                  <div className="text-left">
                    <p className="text-yellow-800 font-medium mb-1">注意事项</p>
                    <ul className="text-yellow-700 text-xs space-y-1">
                      <li>• 验证链接有效期为24小时</li>
                      <li>• 如果没有收到邮件，请检查垃圾邮件文件夹</li>
                      <li>• 验证成功后即可正常使用所有功能</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {error && (
              <div className="mt-4 rounded-md bg-red-50 p-4">
                <div className="text-sm text-red-700">{error}</div>
              </div>
            )}

            {resent && (
              <div className="mt-4 rounded-md bg-green-50 p-4">
                <div className="flex items-center">
                  <CheckCircleIcon className="w-5 h-5 text-green-600 mr-2" />
                  <div className="text-sm text-green-700">
                    验证邮件已重新发送，请检查您的邮箱
                  </div>
                </div>
              </div>
            )}

            <div className="mt-6 space-y-4">
              <button
                onClick={handleResendEmail}
                disabled={resending || !email}
                className="w-full flex justify-center items-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {resending ? (
                  <>
                    <ArrowPathIcon className="w-4 h-4 mr-2 animate-spin" />
                    重发中...
                  </>
                ) : (
                  <>
                    <EnvelopeIcon className="w-4 h-4 mr-2" />
                    重新发送验证邮件
                  </>
                )}
              </button>

              <div className="text-center">
                <Link
                  href="/login"
                  className="text-sm text-blue-600 hover:text-blue-500"
                >
                  返回登录页面
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 帮助信息 */}
      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-gray-100 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-2">需要帮助？</h3>
          <div className="text-xs text-gray-600 space-y-1">
            <p>• 如果长时间未收到邮件，请联系客服</p>
            <p>• 确保邮箱地址输入正确</p>
            <p>• 检查邮箱的垃圾邮件设置</p>
          </div>
          <div className="mt-3">
            <Link
              href="/contact"
              className="text-xs text-blue-600 hover:text-blue-500"
            >
              联系客服 →
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
