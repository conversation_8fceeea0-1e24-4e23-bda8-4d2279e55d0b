export default function Loading() {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center">
      {/* 主加载动画 */}
      <div className="flex flex-col items-center space-y-4">
        {/* Logo加载动画 */}
        <div className="relative">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center animate-pulse">
            <span className="text-white font-bold text-xl">码</span>
          </div>
          <div className="absolute inset-0 w-16 h-16 border-4 border-blue-200 rounded-xl animate-spin border-t-blue-600"></div>
        </div>
        
        {/* 加载文字 */}
        <div className="text-center">
          <h2 className="text-lg font-medium text-gray-900 mb-2">加载中...</h2>
          <p className="text-sm text-gray-600">请稍候，正在为您准备内容</p>
        </div>
        
        {/* 进度条动画 */}
        <div className="w-64 h-2 bg-gray-200 rounded-full overflow-hidden">
          <div className="h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-full animate-pulse"></div>
        </div>
      </div>
      
      {/* 底部提示 */}
      <div className="mt-12 text-center">
        <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
      </div>
    </div>
  )
}
