import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { feedbackSchema } from '@/lib/validations/schemas'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: '未认证' },
        { status: 401 }
      )
    }
    
    const body = await request.json()
    
    // 验证输入数据
    const validation = feedbackSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        { error: '输入数据无效', details: validation.error.errors },
        { status: 400 }
      )
    }
    
    const { claim_id, feedback_data } = validation.data
    
    // 验证领取记录是否存在且属于当前用户
    const { data: claim, error: claimError } = await supabase
      .from('code_claims')
      .select(`
        id,
        user_id,
        feedback_submitted,
        activities (
          feedback_required,
          feedback_questions
        )
      `)
      .eq('id', claim_id)
      .single()
    
    if (claimError) {
      if (claimError.code === 'PGRST116') {
        return NextResponse.json(
          { error: '领取记录不存在' },
          { status: 404 }
        )
      }
      
      console.error('获取领取记录失败:', claimError)
      return NextResponse.json(
        { error: '获取领取记录失败' },
        { status: 500 }
      )
    }
    
    // 检查权限
    if (claim.user_id !== user.id) {
      return NextResponse.json(
        { error: '无权限提交此反馈' },
        { status: 403 }
      )
    }
    
    // 检查是否已经提交过反馈
    if (claim.feedback_submitted) {
      return NextResponse.json(
        { error: '您已经提交过反馈' },
        { status: 409 }
      )
    }
    
    // 检查活动是否需要反馈
    if (!claim.activities.feedback_required) {
      return NextResponse.json(
        { error: '此活动不需要反馈' },
        { status: 400 }
      )
    }
    
    // 验证反馈数据完整性
    const requiredQuestions = claim.activities.feedback_questions || []
    const providedAnswers = Object.keys(feedback_data)
    
    if (requiredQuestions.length > 0) {
      for (let i = 0; i < requiredQuestions.length; i++) {
        if (!providedAnswers.includes(i.toString()) || !feedback_data[i.toString()]?.trim()) {
          return NextResponse.json(
            { error: `请回答第 ${i + 1} 个问题` },
            { status: 400 }
          )
        }
      }
    }
    
    // 更新领取记录，标记为已提交反馈
    const { data: updatedClaim, error: updateError } = await supabase
      .from('code_claims')
      .update({
        feedback_submitted: true,
        feedback_data: feedback_data
      })
      .eq('id', claim_id)
      .select(`
        id,
        feedback_submitted,
        feedback_data,
        activities (
          title,
          apps (
            name
          )
        )
      `)
      .single()
    
    if (updateError) {
      console.error('更新反馈状态失败:', updateError)
      return NextResponse.json(
        { error: '提交反馈失败' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      message: '反馈提交成功',
      claim: updatedClaim
    })
    
  } catch (error) {
    console.error('提交反馈错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
