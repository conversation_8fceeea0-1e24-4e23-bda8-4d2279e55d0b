import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: '未认证' },
        { status: 401 }
      )
    }
    
    // 获取用户在此活动中的领取记录
    const { data: claim, error: claimError } = await supabase
      .from('code_claims')
      .select(`
        id,
        invite_code,
        feedback_submitted,
        claimed_at
      `)
      .eq('activity_id', params.id)
      .eq('user_id', user.id)
      .single()
    
    if (claimError) {
      if (claimError.code === 'PGRST116') {
        // 用户没有领取过此活动的邀请码
        return NextResponse.json(
          { error: '未找到领取记录' },
          { status: 404 }
        )
      }
      
      console.error('获取领取记录失败:', claimError)
      return NextResponse.json(
        { error: '获取领取记录失败' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({ claim })
    
  } catch (error) {
    console.error('获取用户领取信息错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
