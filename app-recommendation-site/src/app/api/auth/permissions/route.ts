import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: '未认证' },
        { status: 401 }
      )
    }
    
    // 获取用户资料
    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('is_verified, is_vip, vip_expires_at')
      .eq('id', user.id)
      .single()
    
    if (profileError) {
      console.error('获取用户资料失败:', profileError)
      return NextResponse.json(
        { error: '获取用户资料失败' },
        { status: 500 }
      )
    }
    
    // 检查VIP状态
    const isVip = profile.is_vip && 
      (!profile.vip_expires_at || new Date(profile.vip_expires_at) > new Date())
    
    const permissions = {
      isAuthenticated: true,
      isVerified: profile.is_verified,
      isVip,
      canCreateAdvancedActivity: isVip,
      canSetVerificationRequirement: isVip,
      canSetSubscriptionRequirement: isVip,
      canSetFeedbackRequirement: isVip
    }
    
    return NextResponse.json(permissions)
    
  } catch (error) {
    console.error('获取权限信息错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
