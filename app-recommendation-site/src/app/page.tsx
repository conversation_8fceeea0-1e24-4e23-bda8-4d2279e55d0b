import Link from 'next/link'
import { createClient } from '@/lib/supabase/server'
import {
  RocketLaunchIcon,
  GiftIcon,
  StarIcon,
  UserGroupIcon,
  TrophyIcon,
  FireIcon
} from '@heroicons/react/24/outline'

interface App {
  id: string
  name: string
  description: string
  icon_url?: string
  category: string
  activity_count: number
}

interface Activity {
  id: string
  title: string
  description: string
  total_codes: number
  used_codes: number
  apps: {
    name: string
    icon_url?: string
  }
}

export default async function Home() {
  // 获取热门应用（按活动数量排序）
  const supabase = await createClient()

  const { data: apps } = await supabase
    .from('apps')
    .select(`
      id,
      name,
      description,
      icon_url,
      category,
      activity_count
    `)
    .eq('is_active', true)
    .order('activity_count', { ascending: false })
    .limit(6)

  // 获取最新活动
  const { data: activities } = await supabase
    .from('activities')
    .select(`
      id,
      title,
      description,
      total_codes,
      used_codes,
      apps (
        name,
        icon_url
      )
    `)
    .eq('is_active', true)
    .order('created_at', { ascending: false })
    .limit(6)
  const features = [
    {
      icon: GiftIcon,
      title: '送码活动',
      description: '参与各种送码活动，获取应用邀请码和优惠',
      color: 'text-blue-600 bg-blue-100'
    },
    {
      icon: TrophyIcon,
      title: '热度排名',
      description: '基于活动数量的应用排名，发现真正活跃的应用',
      color: 'text-orange-600 bg-orange-100'
    },
    {
      icon: StarIcon,
      title: 'VIP特权',
      description: 'VIP用户可设置高级活动条件，获得更多功能',
      color: 'text-yellow-600 bg-yellow-100'
    },
    {
      icon: UserGroupIcon,
      title: '社区互动',
      description: '与开发者和用户互动，分享使用体验和反馈',
      color: 'text-green-600 bg-green-100'
    }
  ]
  
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              发现优质应用
              <span className="block text-blue-600">参与送码活动</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              基于送码活动的应用发现平台，让优质应用被更多人发现，让用户获得更多优惠。
              不再依赖下载量排名，而是通过真实的活动热度来推荐应用。
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/apps"
                className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <RocketLaunchIcon className="w-5 h-5 mr-2" />
                探索应用
              </Link>
              <Link
                href="/activities"
                className="inline-flex items-center px-8 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <GiftIcon className="w-5 h-5 mr-2" />
                查看活动
              </Link>
            </div>
          </div>
          
          {/* 统计数据 */}
          <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">100+</div>
              <div className="text-gray-600">优质应用</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">500+</div>
              <div className="text-gray-600">送码活动</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600">1000+</div>
              <div className="text-gray-600">注册用户</div>
            </div>
          </div>
        </div>
      </section>
      
      {/* 特色功能 */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">平台特色</h2>
            <p className="text-lg text-gray-600">
              创新的应用发现方式，让好应用脱颖而出
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon
              return (
                <div key={index} className="text-center">
                  <div className={`w-16 h-16 ${feature.color} rounded-lg flex items-center justify-center mx-auto mb-4`}>
                    <Icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>
      </section>
      
      {/* 热门应用 */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-2">热门应用</h2>
              <p className="text-gray-600">基于送码活动热度排名的优质应用</p>
            </div>
            <Link
              href="/apps"
              className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium"
            >
              查看全部
              <FireIcon className="w-5 h-5 ml-1" />
            </Link>
          </div>
          
          {apps && apps.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {apps.map((app) => (
                <Link
                  key={app.id}
                  href={`/apps/${app.id}`}
                  className="group bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start space-x-4">
                    {app.icon_url ? (
                      <img
                        src={app.icon_url}
                        alt={app.name}
                        className="w-12 h-12 rounded-lg object-cover flex-shrink-0"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                        <span className="text-white font-bold text-sm">
                          {app.name.charAt(0)}
                        </span>
                      </div>
                    )}
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-medium text-gray-900 group-hover:text-blue-600 mb-2">
                        {app.name}
                      </h3>
                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                        {app.description}
                      </p>
                      <div className="flex items-center justify-between">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {app.category}
                        </span>
                        <div className="flex items-center text-sm text-gray-500">
                          <FireIcon className="w-4 h-4 mr-1" />
                          {app.activity_count} 个活动
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-500 mb-4">暂无应用数据</div>
              <Link
                href="/apps/create"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                发布第一个应用
              </Link>
            </div>
          )}
        </div>
      </section>
      
      {/* 最新活动 */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-2">最新活动</h2>
              <p className="text-gray-600">参与送码活动，获取应用邀请码</p>
            </div>
            <Link
              href="/activities"
              className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium"
            >
              查看全部
              <GiftIcon className="w-5 h-5 ml-1" />
            </Link>
          </div>
          
          {activities && activities.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {activities.map((activity) => (
                <Link
                  key={activity.id}
                  href={`/activities/${activity.id}`}
                  className="group bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start space-x-4">
                    {activity.apps.icon_url ? (
                      <img
                        src={activity.apps.icon_url}
                        alt={activity.apps.name}
                        className="w-12 h-12 rounded-lg object-cover flex-shrink-0"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
                        <GiftIcon className="w-6 h-6 text-white" />
                      </div>
                    )}
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-medium text-gray-900 group-hover:text-blue-600 mb-1">
                        {activity.title}
                      </h3>
                      <p className="text-sm text-gray-600 mb-2">
                        来自 {activity.apps.name}
                      </p>
                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                        {activity.description}
                      </p>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">
                          {activity.total_codes - activity.used_codes} / {activity.total_codes} 剩余
                        </span>
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{
                              width: `${activity.total_codes > 0 ? (activity.used_codes / activity.total_codes) * 100 : 0}%`
                            }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-500 mb-4">暂无活动数据</div>
              <Link
                href="/apps"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                发布应用后创建活动
              </Link>
            </div>
          )}
        </div>
      </section>
      
      {/* CTA Section */}
      <section className="py-16 bg-blue-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            准备好发布您的应用了吗？
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            加入我们的平台，通过送码活动让更多用户发现您的应用
          </p>
          <Link
            href="/apps/create"
            className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <RocketLaunchIcon className="w-5 h-5 mr-2" />
            立即发布应用
          </Link>
        </div>
      </section>
    </div>
  )
}
