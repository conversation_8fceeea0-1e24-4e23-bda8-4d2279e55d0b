'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/auth/AuthProvider'
import FeedbackForm from './FeedbackForm'
import { 
  ChatBubbleLeftRightIcon, 
  CheckCircleIcon,
  ExclamationTriangleIcon 
} from '@heroicons/react/24/outline'

interface UserClaim {
  id: string
  invite_code: string
  feedback_submitted: boolean
  claimed_at: string
}

interface ActivityFeedbackSectionProps {
  activityId: string
  feedbackRequired: boolean
  feedbackQuestions: string[]
}

export default function ActivityFeedbackSection({ 
  activityId, 
  feedbackRequired, 
  feedbackQuestions 
}: ActivityFeedbackSectionProps) {
  const [userClaim, setUserClaim] = useState<UserClaim | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const { user } = useAuth()

  useEffect(() => {
    const fetchUserClaim = async () => {
      if (!user) {
        setLoading(false)
        return
      }

      try {
        const response = await fetch(`/api/activities/${activityId}/user-claim`)
        if (response.ok) {
          const data = await response.json()
          setUserClaim(data.claim)
        } else if (response.status !== 404) {
          // 404 表示用户没有领取过，这是正常情况
          const errorData = await response.json()
          setError(errorData.error || '获取领取信息失败')
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : '获取领取信息失败')
      } finally {
        setLoading(false)
      }
    }

    fetchUserClaim()
  }, [activityId, user])

  const handleFeedbackSuccess = () => {
    // 更新用户领取状态
    if (userClaim) {
      setUserClaim({
        ...userClaim,
        feedback_submitted: true
      })
    }
  }

  if (!user) {
    return null
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-20 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center">
          <ExclamationTriangleIcon className="w-6 h-6 text-red-600 mr-3" />
          <div>
            <h3 className="text-lg font-medium text-red-800">加载失败</h3>
            <p className="text-red-700 mt-1">{error}</p>
          </div>
        </div>
      </div>
    )
  }

  // 用户没有领取过邀请码
  if (!userClaim) {
    return null
  }

  // 活动不需要反馈
  if (!feedbackRequired) {
    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-center">
          <CheckCircleIcon className="w-6 h-6 text-blue-600 mr-3" />
          <div>
            <h3 className="text-lg font-medium text-blue-800">邀请码已领取</h3>
            <p className="text-blue-700 mt-1">
              您已成功领取邀请码：<span className="font-mono font-semibold">{userClaim.invite_code}</span>
            </p>
            <p className="text-blue-600 text-sm mt-1">
              领取时间：{new Date(userClaim.claimed_at).toLocaleString()}
            </p>
          </div>
        </div>
      </div>
    )
  }

  // 已经提交过反馈
  if (userClaim.feedback_submitted) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <div className="flex items-center">
          <CheckCircleIcon className="w-6 h-6 text-green-600 mr-3" />
          <div>
            <h3 className="text-lg font-medium text-green-800">反馈已提交</h3>
            <p className="text-green-700 mt-1">
              感谢您的反馈！您已完成此活动的所有要求。
            </p>
            <p className="text-green-600 text-sm mt-1">
              邀请码：<span className="font-mono font-semibold">{userClaim.invite_code}</span>
            </p>
          </div>
        </div>
      </div>
    )
  }

  // 需要提交反馈
  return (
    <div className="space-y-6">
      {/* 领取成功提示 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-center">
          <CheckCircleIcon className="w-6 h-6 text-blue-600 mr-3" />
          <div>
            <h3 className="text-lg font-medium text-blue-800">邀请码已领取</h3>
            <p className="text-blue-700 mt-1">
              您已成功领取邀请码：<span className="font-mono font-semibold">{userClaim.invite_code}</span>
            </p>
            <p className="text-blue-600 text-sm mt-1">
              领取时间：{new Date(userClaim.claimed_at).toLocaleString()}
            </p>
          </div>
        </div>
      </div>

      {/* 反馈表单 */}
      <FeedbackForm
        claimId={userClaim.id}
        questions={feedbackQuestions}
        onSuccess={handleFeedbackSuccess}
      />
    </div>
  )
}
