'use client'

import { useAuth } from '@/components/auth/AuthProvider'
import { PlusIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'

interface CreateActivityButtonProps {
  appId: string
  appCreatedBy: string
  className?: string
}

export default function CreateActivityButton({ 
  appId, 
  appCreatedBy, 
  className = '' 
}: CreateActivityButtonProps) {
  const { user } = useAuth()

  // 只有应用的创建者才能创建活动
  if (!user || user.id !== appCreatedBy) {
    return null
  }

  return (
    <Link
      href={`/apps/${appId}/activities/create`}
      className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 ${className}`}
    >
      <PlusIcon className="w-4 h-4 mr-2" />
      创建活动
    </Link>
  )
}
